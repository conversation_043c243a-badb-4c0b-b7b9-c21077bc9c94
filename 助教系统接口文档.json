{"openapi": "3.0.1", "info": {"title": "teachingassistant", "description": "", "version": "1.0.0"}, "tags": [{"name": "认证模块"}, {"name": "个人中心"}, {"name": "权限管理"}, {"name": "人员信息管理"}, {"name": "教学管理"}, {"name": "校长运营管理-约课中心"}, {"name": "校长运营管理-消息中枢"}, {"name": "财务管理"}, {"name": "教师-教学工作"}, {"name": "学生管理"}, {"name": "工资查询"}, {"name": "消息管理"}, {"name": "个人信息"}, {"name": "学校管理"}, {"name": "校长管理"}, {"name": "管理员模块-用户管理"}], "paths": {"/api/login": {"post": {"summary": "用户登录", "deprecated": false, "description": "账号密码登录，系统自动识别角色", "tags": ["认证模块"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}, "responses": {"200": {"description": "登录成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginResponse"}}}, "headers": {}}, "401": {"description": "用户名或密码错误", "headers": {}}, "403": {"description": "账户已禁用", "headers": {}}}, "security": []}}, "/api/logout": {"post": {"summary": "用户登出", "deprecated": false, "description": "", "tags": ["认证模块"], "parameters": [], "responses": {"200": {"description": "登出成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}}}}}, "headers": {}}}, "security": []}}, "/api/user/profile": {"get": {"summary": "获取当前用户信息", "deprecated": false, "description": "", "tags": ["个人中心"], "parameters": [], "responses": {"200": {"description": "成功获取用户信息", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfile"}}}, "headers": {}}}, "security": []}}, "/api/user/password": {"put": {"summary": "修改密码", "deprecated": false, "description": "", "tags": ["个人中心"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePasswordRequest"}}}}, "responses": {"200": {"description": "密码修改成功", "headers": {}}, "401": {"description": "原始密码错误", "headers": {}}}, "security": []}}, "/api/user/phone": {"put": {"summary": "绑定手机号", "deprecated": false, "description": "", "tags": ["个人中心"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"phone": {"type": "string", "example": "13800138000"}}, "required": ["phone"]}}}}, "responses": {"200": {"description": "手机绑定成功", "headers": {}}}, "security": []}}, "/api/user/settings": {"get": {"summary": "获取用户设置", "deprecated": false, "description": "", "tags": ["个人中心"], "parameters": [], "responses": {"200": {"description": "成功获取设置", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSettings"}}}, "headers": {}}}, "security": []}, "put": {"summary": "更新用户设置", "deprecated": false, "description": "", "tags": ["个人中心"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSettings"}}}}, "responses": {"200": {"description": "设置更新成功", "headers": {}}}, "security": []}}, "/api/user/logs": {"get": {"summary": "获取用户操作日志", "deprecated": false, "description": "", "tags": ["个人中心"], "parameters": [{"name": "page", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "size", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "成功获取日志", "content": {"application/json": {"schema": {"type": "object", "properties": {"total": {"type": "integer"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/OperationLog"}}}}}}, "headers": {}}}, "security": []}}, "/api/menu": {"get": {"summary": "获取动态菜单", "deprecated": false, "description": "根据用户角色返回权限菜单", "tags": ["权限管理"], "parameters": [], "responses": {"200": {"description": "成功获取菜单", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}}}}, "headers": {}}}, "security": []}}, "/api/principal/teachers": {"get": {"summary": "查询所有老师信息", "deprecated": false, "description": "分页查询本校所有老师信息，支持姓名搜索", "tags": ["人员信息管理"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "size", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "default": 10}}, {"name": "name", "in": "query", "description": "老师姓名(模糊查询)", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取老师列表", "content": {"*/*": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "example": 25}, "teachers": {"type": "array", "items": {"$ref": "#/components/schemas/TeacherBasicInfo"}}}}}}}}, "headers": {}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/api/principal/teachers/{teacherId}": {"get": {"summary": "获取老师详细信息", "deprecated": false, "description": "获取老师详情（含关联学生及工资详情）", "tags": ["人员信息管理"], "parameters": [{"name": "teacherId", "in": "path", "description": "老师ID", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功获取老师详情", "content": {"*/*": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"$ref": "#/components/schemas/TeacherDetailInfo"}}}}}, "headers": {}}, "404": {"description": "老师不存在", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/api/principal/students": {"get": {"summary": "查询所有学生信息", "deprecated": false, "description": "分页查询本校所有学生信息，支持姓名/班级搜索", "tags": ["人员信息管理"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "size", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "default": 10}}, {"name": "name", "in": "query", "description": "学生姓名(模糊查询)", "required": false, "schema": {"type": "string"}}, {"name": "classId", "in": "query", "description": "班级ID", "required": false, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功获取学生列表", "content": {"*/*": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "example": 120}, "students": {"type": "array", "items": {"$ref": "#/components/schemas/StudentBasicInfo"}}}}}}}}, "headers": {}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/api/principal/students/{studentId}": {"get": {"summary": "获取学生详细信息", "deprecated": false, "description": "获取学生基本信息及未付款金额", "tags": ["人员信息管理"], "parameters": [{"name": "studentId", "in": "path", "description": "学生ID", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功获取学生详情", "content": {"*/*": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "success"}, "data": {"$ref": "#/components/schemas/StudentDetailInfo"}}}}}, "headers": {}}, "404": {"description": "学生不存在", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/principal/teach/schedule/batch": {"post": {"summary": "批量排课", "deprecated": false, "description": "根据周期自动生成重复课程（支持排除节假日）", "tags": ["教学管理"], "parameters": [{"name": "Authorization", "in": "header", "description": "JWT认证token", "required": true, "example": "", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"teacherId": {"type": "integer", "example": 1025}, "studentId": {"type": "integer", "example": 3058}, "classroomId": {"type": "integer", "example": 12}, "startDate": {"type": "string", "format": "date", "example": "2025-09-01"}, "endDate": {"type": "string", "format": "date", "example": "2025-12-31"}, "repeatDays": {"type": "array", "items": {"type": "string", "enum": ["MON", "TUE", "WED", "THU", "FRI", "SAT", "SUN"]}, "example": ["MON", "WED", "FRI"]}, "startTime": {"type": "string", "format": "time", "example": "14:30:00"}, "endTime": {"type": "string", "format": "time", "example": "16:00:00"}, "price": {"type": "number", "format": "float", "example": 150}, "gradeLevel": {"type": "string", "example": "高一"}, "excludeHolidays": {"type": "boolean", "default": true}}}}}}, "responses": {"200": {"description": "排课成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"conflictCount": {"type": "integer", "example": 2}, "successCount": {"type": "integer", "example": 36}, "conflicts": {"type": "array", "items": {"type": "object", "properties": {"date": {"type": "string", "example": "2025-10-01"}, "reason": {"type": "string", "example": "节假日冲突"}}}}}}}}, "headers": {}}, "409": {"description": "存在资源冲突", "content": {"application/json": {"schema": {"type": "object", "properties": {"error": {"type": "string", "example": "该教室在2025-09-03 14:30已被占用"}}}}}, "headers": {}}}, "security": []}}, "/principal/teach/schedule/{courseId}": {"put": {"summary": "调整单节课程", "deprecated": false, "description": "拖拽式修改课程时间/教室（自动通知相关老师）", "tags": ["教学管理"], "parameters": [{"name": "courseId", "in": "path", "description": "", "required": true, "example": 8805, "schema": {"type": "integer"}}, {"name": "Authorization", "in": "header", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"newDate": {"type": "string", "format": "date", "example": "2025-09-05"}, "newStartTime": {"type": "string", "format": "time", "example": "15:00:00"}, "newEndTime": {"type": "string", "format": "time", "example": "16:30:00"}, "newClassroomId": {"type": "integer", "example": 15}, "notifyTeacher": {"type": "boolean", "default": true}}}}}}, "responses": {"200": {"description": "调整成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"notificationId": {"type": "integer", "example": 33258}}}}}, "headers": {}}, "400": {"description": "新时间冲突", "headers": {}}}, "security": []}}, "/principal/teach/classrooms": {"get": {"summary": "教室状态查询", "deprecated": false, "description": "实时教室使用状态看板", "tags": ["教学管理"], "parameters": [{"name": "date", "in": "query", "description": "", "required": true, "example": "2025-09-01", "schema": {"type": "string", "format": "date"}}, {"name": "Authorization", "in": "header", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "教室状态数据", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"classroomId": {"type": "integer", "example": 12}, "name": {"type": "string", "example": "物理实验室"}, "status": {"type": "string", "enum": ["available", "in_use", "maintenance"], "example": "in_use"}, "currentCourse": {"type": "object", "properties": {"courseId": {"type": "integer", "example": 8805}, "teacherName": {"type": "string", "example": "张老师"}, "studentName": {"type": "string", "example": "<PERSON>"}, "timeRange": {"type": "string", "example": "14:30-16:00"}}}, "utilizationRate": {"type": "number", "format": "float", "example": 75.3}}}}}}, "headers": {}}}, "security": []}}, "/principal/teach/classes": {"post": {"summary": "创建班级", "deprecated": false, "description": "", "tags": ["教学管理"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"className": {"type": "string", "example": "高一(3)班"}, "mainTeacherId": {"type": "integer", "example": 1025}}}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"classId": {"type": "integer", "example": 28}}}}}, "headers": {}}}, "security": []}, "get": {"summary": "班级列表查询", "deprecated": false, "description": "", "tags": ["教学管理"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "班级数据", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object", "properties": {"classId": {"type": "integer", "example": 28}, "className": {"type": "string", "example": "高一(3)班"}, "mainTeacher": {"type": "string", "example": "张老师"}, "studentCount": {"type": "integer", "example": 45}, "teacherCount": {"type": "integer", "example": 8}}}}}}, "headers": {}}}, "security": []}}, "/principal/teach/classes/{classId}": {"put": {"summary": "修改班级信息", "deprecated": false, "description": "", "tags": ["教学管理"], "parameters": [{"name": "classId", "in": "path", "description": "", "required": true, "example": 28, "schema": {"type": "integer"}}, {"name": "Authorization", "in": "header", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"newClassName": {"type": "string", "example": "高一(3)班-火箭班"}, "newMainTeacherId": {"type": "integer", "example": 1030}}}}}}, "responses": {"200": {"description": "修改成功", "headers": {}}}, "security": []}, "delete": {"summary": "删除班级", "deprecated": false, "description": "", "tags": ["教学管理"], "parameters": [{"name": "classId", "in": "path", "description": "", "required": true, "example": 28, "schema": {"type": "integer"}}, {"name": "Authorization", "in": "header", "description": "", "required": true, "example": "", "schema": {"type": "string"}}], "responses": {"204": {"description": "删除成功", "headers": {}}}, "security": []}}, "/api/operation/bookings": {"get": {"summary": "获取约课请求列表", "deprecated": false, "description": "", "tags": ["校长运营管理-约课中心"], "parameters": [{"name": "status", "in": "query", "description": "约课状态过滤", "required": false, "schema": {"type": "string", "enum": ["pending", "confirmed", "rejected"]}}, {"name": "startDate", "in": "query", "description": "约课开始日期", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "约课结束日期", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "成功获取约课列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"total": {"type": "integer", "example": 15}, "bookings": {"type": "array", "items": {"$ref": "#/components/schemas/BookingInfo"}}}}}}, "headers": {}}}, "security": []}, "post": {"summary": "处理约课请求", "deprecated": false, "description": "", "tags": ["校长运营管理-约课中心"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HandleBookingRequest"}}}}, "responses": {"200": {"description": "处理成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "约课处理成功"}}}}}, "headers": {}}}, "security": []}}, "/api/operation/bookings/conflicts": {"get": {"summary": "获取约课冲突解决方案", "deprecated": false, "description": "", "tags": ["校长运营管理-约课中心"], "parameters": [{"name": "bookingId", "in": "query", "description": "约课ID", "required": true, "schema": {"type": "integer"}}], "responses": {"200": {"description": "冲突解决方案列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ConflictSolution"}}}}, "headers": {}}}, "security": []}}, "/api/operation/messages": {"get": {"summary": "获取消息列表", "deprecated": false, "description": "", "tags": ["校长运营管理-消息中枢"], "parameters": [{"name": "type", "in": "query", "description": "消息类型过滤", "required": false, "schema": {"type": "string", "enum": ["all", "booking", "notification", "payment"]}}, {"name": "priority", "in": "query", "description": "优先级过滤", "required": false, "schema": {"type": "string", "enum": ["all", "normal", "urgent"]}}], "responses": {"200": {"description": "成功获取消息列表", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MessageInfo"}}}}, "headers": {}}}, "security": []}}, "/api/operation/messages/{messageId}/status": {"patch": {"summary": "更新消息状态", "deprecated": false, "description": "", "tags": ["校长运营管理-消息中枢"], "parameters": [{"name": "messageId", "in": "path", "description": "消息ID", "required": true, "example": 0, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["read", "confirmed"]}}, "required": ["status"]}}}}, "responses": {"200": {"description": "状态更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "消息状态更新成功"}}}}}, "headers": {}}}, "security": []}}, "/api/operation/messages/urgent": {"post": {"summary": "发送紧急通知", "deprecated": false, "description": "", "tags": ["校长运营管理-消息中枢"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UrgentMessageRequest"}}}}, "responses": {"200": {"description": "发送成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "紧急通知发送成功"}}}}}, "headers": {}}}, "security": []}}, "/api/principal/salaries": {"get": {"summary": "获取老师工资明细列表", "deprecated": false, "description": "分页查询本校所有老师的工资明细（数据隔离到当前校长所属学校）", "tags": ["财务管理"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "default": 10}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "description": "老师姓名模糊搜索", "required": false, "schema": {"type": "string"}}, {"name": "period", "in": "query", "description": "薪资周期（格式 YYYY-MM）", "required": false, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "成功获取工资列表", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/SalaryPageResult"}}}, "headers": {}}, "403": {"description": "无权限操作", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/api/principal/students/unpaid": {"get": {"summary": "获取未付款学生列表", "deprecated": false, "description": "分页查询本校未付款学生信息（数据隔离到当前校长所属学校）", "tags": ["财务管理"], "parameters": [{"name": "minAmount", "in": "query", "description": "最小未付金额", "required": false, "schema": {"type": "number", "format": "decimal"}}, {"name": "maxAmount", "in": "query", "description": "最大未付金额", "required": false, "schema": {"type": "number", "format": "decimal"}}, {"name": "classId", "in": "query", "description": "按班级筛选", "required": false, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功获取学生列表", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/UnpaidStudentList"}}}, "headers": {}}, "403": {"description": "无权限操作", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/api/principal/students/{studentId}/payments": {"get": {"summary": "获取学生缴费记录", "deprecated": false, "description": "查询指定学生的历史缴费记录（按学年分组）", "tags": ["财务管理"], "parameters": [{"name": "studentId", "in": "path", "description": "学生ID", "required": true, "example": 0, "schema": {"type": "integer"}}, {"name": "schoolYear", "in": "query", "description": "筛选学年（格式 2023-2024）", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取缴费记录", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StudentPaymentHistory"}}}, "headers": {}}, "404": {"description": "学生不存在", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/api/principal/students/unpaid/export": {"post": {"summary": "导出未付款学生列表", "deprecated": false, "description": "导出CSV格式的未付款学生清单", "tags": ["财务管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnpaidExportRequest"}}}}, "responses": {"200": {"description": "导出成功", "content": {"*/*": {"schema": {"type": "string", "format": "binary"}}}, "headers": {}}, "403": {"description": "无权限操作", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/teacher/courses/schedule": {"get": {"summary": "获取老师课程表", "deprecated": false, "description": "支持日/周/月视图切换，课前1小时自动推送提醒", "tags": ["教师-教学工作"], "parameters": [{"name": "viewType", "in": "query", "description": "视图类型(day/week/month)", "required": true, "schema": {"type": "string", "enum": ["day", "week", "month"]}}, {"name": "date", "in": "query", "description": "查询日期(格式yyyy-MM-dd)", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "成功获取课程表", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CourseSchedule"}}}}}}, "headers": {}}, "401": {"description": "未授权访问", "headers": {}}}, "security": []}}, "/teacher/attendance": {"post": {"summary": "记录学生考勤", "deprecated": false, "description": "人脸识别快速签到，缺勤自动通知家长", "tags": ["教师-教学工作"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AttendanceRecord"}}}}}, "responses": {"200": {"description": "考勤记录成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "考勤记录已保存"}}}}}, "headers": {}}, "400": {"description": "数据校验失败", "headers": {}}}, "security": []}}, "/teacher/attendance/history": {"get": {"summary": "查询考勤历史", "deprecated": false, "description": "按时间范围查询考勤记录", "tags": ["教师-教学工作"], "parameters": [{"name": "startDate", "in": "query", "description": "开始日期(格式yyyy-MM-dd)", "required": true, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "结束日期(格式yyyy-MM-dd)", "required": true, "schema": {"type": "string", "format": "date"}}], "responses": {"200": {"description": "成功获取考勤记录", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/AttendanceHistory"}}}}}}, "headers": {}}}, "security": []}}, "/api/teacher/students": {"get": {"summary": "获取负责的学生列表", "deprecated": false, "description": "分页获取当前老师负责的学生列表（包含基础信息）", "tags": ["学生管理"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "pageSize", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "default": 10}}, {"name": "className", "in": "query", "description": "班级名称筛选", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取学生列表", "content": {"*/*": {"schema": {"type": "object", "properties": {"total": {"type": "integer", "example": 25}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/StudentBasicInfo"}}}}}}, "headers": {}}, "401": {"description": "未授权访问", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}, "403": {"description": "禁止访问其他学校数据", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/api/teacher/students/{studentId}": {"get": {"summary": "获取学生详细信息", "deprecated": false, "description": "获取单个学生的完整档案（含出勤统计和学习进度）", "tags": ["学生管理"], "parameters": [{"name": "studentId", "in": "path", "description": "学生ID", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功获取学生详情", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/StudentDetailInfo"}}}, "headers": {}}, "403": {"description": "禁止访问其他学校数据", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}, "404": {"description": "学生不存在或未关联", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/api/teacher/students/{studentId}/attendance": {"get": {"summary": "获取学生出勤记录", "deprecated": false, "description": "分页获取指定学生的历史出勤记录", "tags": ["学生管理"], "parameters": [{"name": "studentId", "in": "path", "description": "学生ID", "required": true, "example": 0, "schema": {"type": "integer"}}, {"name": "startDate", "in": "query", "description": "筛选开始日期 (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "endDate", "in": "query", "description": "筛选结束日期 (YYYY-MM-DD)", "required": false, "schema": {"type": "string", "format": "date"}}, {"name": "status", "in": "query", "description": "出勤状态筛选", "required": false, "schema": {"type": "string", "enum": ["present", "absent", "late"]}}], "responses": {"200": {"description": "成功获取出勤记录", "content": {"*/*": {"schema": {"type": "object", "properties": {"studentId": {"type": "integer", "example": 1001}, "attendanceStats": {"$ref": "#/components/schemas/AttendanceStats"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/AttendanceRecord"}}}}}}, "headers": {}}, "403": {"description": "禁止访问其他学生数据", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/api/teacher/students/{studentId}/contact": {"get": {"summary": "获取学生联系方式", "deprecated": false, "description": "获取学生及家长的联系方式（含CTI集成信息）", "tags": ["学生管理"], "parameters": [{"name": "studentId", "in": "path", "description": "学生ID", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功获取联系方式", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ContactInfo"}}}, "headers": {}}, "403": {"description": "禁止访问其他学生数据", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/api/teacher/students/search": {"get": {"summary": "搜索学生", "deprecated": false, "description": "按姓名/班级快速搜索负责的学生", "tags": ["学生管理"], "parameters": [{"name": "keyword", "in": "query", "description": "姓名或班级关键词", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "成功获取搜索结果", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StudentSearchResult"}}}}, "headers": {}}, "403": {"description": "禁止跨学校搜索", "content": {"application/json": {"schema": {"type": "object", "properties": {}}}}, "headers": {}}}, "security": []}}, "/api/teacher/salaries": {"get": {"summary": "获取工资列表", "deprecated": false, "description": "查询当前登录老师的工资列表（按月汇总）", "tags": ["工资查询"], "parameters": [{"name": "year", "in": "query", "description": "筛选年份", "required": false, "schema": {"type": "integer", "example": 2023}}, {"name": "month", "in": "query", "description": "筛选月份", "required": false, "schema": {"type": "integer", "example": 8}}, {"name": "page", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "size", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "成功获取工资列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"total": {"type": "integer", "example": 5}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/SalarySummary"}}}}}}, "headers": {}}}, "security": []}}, "/api/teacher/salaries/{salaryId}": {"get": {"summary": "获取工资单详情", "deprecated": false, "description": "查看指定工资单的详细构成", "tags": ["工资查询"], "parameters": [{"name": "salaryId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功获取工资详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SalaryDetail"}}}, "headers": {}}}, "security": []}}, "/api/teacher/messages": {"get": {"summary": "获取消息列表", "deprecated": false, "description": "分页查询当前老师的消息（支持状态/类型/优先级过滤）", "tags": ["消息管理"], "parameters": [{"name": "status", "in": "query", "description": "", "required": false, "schema": {"type": "string", "enum": ["unread", "read", "confirmed"], "default": "unread"}}, {"name": "type", "in": "query", "description": "", "required": false, "schema": {"type": "string", "enum": ["booking", "notification", "payment"]}}, {"name": "priority", "in": "query", "description": "", "required": false, "schema": {"type": "string", "enum": ["normal", "urgent"]}}, {"name": "page", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "size", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "成功获取消息列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"total": {"type": "integer", "example": 15}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/MessageSummary"}}}}}}, "headers": {}}}, "security": []}}, "/api/teacher/messages/{messageId}": {"get": {"summary": "获取消息详情", "deprecated": false, "description": "查看消息详细信息", "tags": ["消息管理"], "parameters": [{"name": "messageId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功获取消息详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageDetail"}}}, "headers": {}}}, "security": []}, "patch": {"summary": "更新消息状态", "deprecated": false, "description": "标记消息为已读或已确认（校长通知需确认）", "tags": ["消息管理"], "parameters": [{"name": "messageId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"action": {"type": "string", "enum": ["mark_as_read", "confirm"], "example": "mark_as_read"}}, "required": ["action"]}}}}, "responses": {"200": {"description": "状态更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "消息状态已更新"}}}}}, "headers": {}}}, "security": []}}, "/api/teacher/profile": {"get": {"summary": "获取教师档案", "deprecated": false, "description": "获取当前登录教师的基本信息", "tags": ["个人信息"], "parameters": [], "responses": {"200": {"description": "成功获取教师档案", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherProfile"}}}, "headers": {}}}, "security": []}, "put": {"summary": "更新教师档案", "deprecated": false, "description": "修改个人信息（手机号/密码等）", "tags": ["个人信息"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TeacherProfileUpdate"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "message": {"type": "string", "example": "个人信息更新成功"}}}}}, "headers": {}}}, "security": []}}, "/admin/schools": {"get": {"summary": "获取学校列表", "deprecated": false, "description": "", "tags": ["学校管理"], "parameters": [{"name": "status", "in": "query", "description": "学校状态过滤", "required": false, "schema": {"type": "string", "enum": ["active", "inactive"]}}, {"name": "page", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "size", "in": "query", "description": "", "required": false, "schema": {"type": "integer", "default": 20}}], "responses": {"200": {"description": "学校列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"total": {"type": "integer"}, "schools": {"type": "array", "items": {"$ref": "#/components/schemas/SchoolInfo"}}}}}}, "headers": {}}}, "security": []}, "post": {"summary": "创建学校", "deprecated": false, "description": "", "tags": ["学校管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSchoolRequest"}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchoolInfo"}}}, "headers": {}}}, "security": []}}, "/admin/schools/{schoolId}": {"put": {"summary": "编辑学校信息", "deprecated": false, "description": "", "tags": ["学校管理"], "parameters": [{"name": "schoolId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateSchoolRequest"}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchoolInfo"}}}, "headers": {}}}, "security": []}, "patch": {"summary": "设置学校状态", "deprecated": false, "description": "", "tags": ["学校管理"], "parameters": [{"name": "schoolId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "enum": ["active", "inactive"]}}}}}}, "responses": {"200": {"description": "状态更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"schoolId": {"type": "integer"}, "status": {"type": "string"}}}}}, "headers": {}}}, "security": []}}, "/api/admin/principals": {"get": {"summary": "获取校长列表", "deprecated": false, "description": "分页查询系统所有校长信息", "tags": ["校长管理"], "parameters": [{"name": "page", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "size", "in": "query", "description": "每页数量", "required": false, "schema": {"type": "integer", "default": 20}}, {"name": "schoolId", "in": "query", "description": "学校ID", "required": false, "schema": {"type": "integer"}}], "responses": {"200": {"description": "成功获取校长列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"total": {"type": "integer", "example": 25}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/PrincipalInfo"}}}}}}}}, "headers": {}}, "401": {"description": "未认证或凭证无效", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 401}, "message": {"type": "string", "example": "身份认证失败"}}}}}, "headers": {}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 403}, "message": {"type": "string", "example": "没有操作权限"}}}}}, "headers": {}}}, "security": []}, "post": {"summary": "创建校长账号", "deprecated": false, "description": "为指定学校创建新的校长账号", "tags": ["校长管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePrincipalRequest"}}}}, "responses": {"201": {"description": "校长账号创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 201}, "data": {"type": "object", "properties": {"userId": {"type": "integer", "example": 1024}, "username": {"type": "string", "example": "principal_zhang"}}}}}}}, "headers": {}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "409": {"description": "用户名已存在", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}}, "security": []}}, "/api/admin/principals/batch-import": {"post": {"summary": "批量导入校长账号", "deprecated": false, "description": "通过Excel文件批量导入校长账号", "tags": ["校长管理"], "parameters": [], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "description": "Excel文件(支持xlsx格式)", "example": ""}}}}}}, "responses": {"200": {"description": "批量导入成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "object", "properties": {"successCount": {"type": "integer", "example": 15}, "failureCount": {"type": "integer", "example": 2}, "failureReasons": {"type": "array", "items": {"type": "string"}, "example": ["第3行: 学校ID不存在", "第7行: 用户名已存在"]}}}}}}}, "headers": {}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "415": {"description": "不支持的文件类型", "headers": {}}}, "security": []}}, "/api/admin/principals/{userId}/reset-password": {"put": {"summary": "重置校长密码", "deprecated": false, "description": "重置指定校长的登录密码", "tags": ["校长管理"], "parameters": [{"name": "userId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"200": {"description": "密码重置成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "string", "example": "新密码已发送至绑定手机"}}}}}, "headers": {}}, "404": {"description": "资源不存在", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "校长账号不存在"}}}}}, "headers": {}}, "422": {"description": "未绑定手机无法重置", "headers": {}}}, "security": []}}, "/api/admin/principals/{userId}/unbind-mfa": {"put": {"summary": "解绑MFA设备", "deprecated": false, "description": "解除校长账号的多因素认证设备绑定", "tags": ["校长管理"], "parameters": [{"name": "userId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"200": {"description": "MFA设备解绑成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"type": "string", "example": "MFA设备已解绑"}}}}}, "headers": {}}, "404": {"description": "资源不存在", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "校长账号不存在"}}}}}, "headers": {}}}, "security": []}}, "/api/admin/principals/{userId}": {"put": {"summary": "更新校长信息", "deprecated": false, "description": "更新校长基本信息", "tags": ["校长管理"], "parameters": [{"name": "userId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"realName": {"type": "string", "example": "张校长"}, "phone": {"type": "string", "example": "13800138000"}, "schoolId": {"type": "integer", "example": 1001}}}}}}, "responses": {"200": {"description": "更新成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 200}, "data": {"$ref": "#/components/schemas/PrincipalInfo"}}}}}, "headers": {}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}, "headers": {}}, "404": {"description": "资源不存在", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "校长账号不存在"}}}}}, "headers": {}}}, "security": []}, "delete": {"summary": "删除校长账号", "deprecated": false, "description": "删除指定校长账号（逻辑删除）", "tags": ["校长管理"], "parameters": [{"name": "userId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"204": {"description": "删除成功", "headers": {}}, "400": {"description": "存在关联数据无法删除", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "该校长管理的学生数据未转移，无法删除"}}}}}, "headers": {}}, "404": {"description": "资源不存在", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "校长账号不存在"}}}}}, "headers": {}}}, "security": []}}, "/admin/users": {"get": {"summary": "查询用户列表", "deprecated": false, "description": "### 权限要求\n- 仅超级管理员可用  \n### 功能说明\n支持按角色/学校/姓名等多条件筛选用户\n", "tags": ["管理员模块-用户管理"], "parameters": [{"name": "role", "in": "query", "description": "用户角色(super_admin/principal/teacher)", "required": false, "schema": {"type": "string"}}, {"name": "schoolId", "in": "query", "description": "学校ID", "required": false, "schema": {"type": "integer"}}, {"name": "realName", "in": "query", "description": "真实姓名(模糊匹配)", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "页码(默认1)", "required": false, "schema": {"type": "integer", "default": 1}}, {"name": "size", "in": "query", "description": "每页数量(默认10)", "required": false, "schema": {"type": "integer", "default": 10}}], "responses": {"200": {"description": "用户列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"total": {"type": "integer", "example": 35}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/UserInfo"}}}}}}, "headers": {}}}, "security": []}, "post": {"summary": "创建用户", "deprecated": false, "description": "### 权限要求\n- 仅超级管理员可用\n", "tags": ["管理员模块-用户管理"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}, "responses": {"201": {"description": "创建成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"userId": {"type": "integer", "example": 10001}}}}}, "headers": {}}}, "security": []}}, "/admin/users/{userId}": {"get": {"summary": "获取用户详情", "deprecated": false, "description": "### 权限要求\n- 仅超级管理员可用\n", "tags": ["管理员模块-用户管理"], "parameters": [{"name": "userId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"200": {"description": "用户详情", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDetail"}}}, "headers": {}}}, "security": []}, "put": {"summary": "更新用户信息", "deprecated": false, "description": "### 权限要求\n- 仅超级管理员可用\n", "tags": ["管理员模块-用户管理"], "parameters": [{"name": "userId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "更新成功", "headers": {}}}, "security": []}, "delete": {"summary": "删除用户", "deprecated": false, "description": "### 权限要求\n- 仅超级管理员可用  \n### 约束条件\n- 存在关联数据(如排课记录)时不可删除\n", "tags": ["管理员模块-用户管理"], "parameters": [{"name": "userId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"200": {"description": "删除成功", "headers": {}}}, "security": []}}, "/admin/users/batch-import": {"post": {"summary": "批量导入用户", "deprecated": false, "description": "### 权限要求\n- 仅超级管理员可用  \n### 文件格式\n- 支持Excel格式\n", "tags": ["管理员模块-用户管理"], "parameters": [], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary", "example": ""}}}}}}, "responses": {"200": {"description": "导入结果", "content": {"application/json": {"schema": {"type": "object", "properties": {"successCount": {"type": "integer", "example": 23}, "failureList": {"type": "array", "items": {"type": "string"}, "example": ["行号3: 用户名重复", "行号7: 学校ID不存在"]}}}}}, "headers": {}}}, "security": []}}, "/admin/users/{userId}/reset-password": {"put": {"summary": "重置用户密码", "deprecated": false, "description": "### 权限要求\n- 仅超级管理员可用\n", "tags": ["管理员模块-用户管理"], "parameters": [{"name": "userId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"200": {"description": "重置成功", "content": {"application/json": {"schema": {"type": "object", "properties": {"newPassword": {"type": "string", "example": "Tmp@123456"}}}}}, "headers": {}}}, "security": []}}, "/admin/users/{userId}/unbind-mfa": {"put": {"summary": "解绑MFA设备", "deprecated": false, "description": "### 权限要求\n- 仅超级管理员可用\n", "tags": ["管理员模块-用户管理"], "parameters": [{"name": "userId", "in": "path", "description": "", "required": true, "example": 0, "schema": {"type": "integer"}}], "responses": {"200": {"description": "解绑成功", "headers": {}}}, "security": []}}}, "components": {"schemas": {"LoginRequest": {"type": "object", "properties": {"username": {"type": "string", "example": "admin"}, "password": {"type": "string", "example": "password123"}, "captcha": {"type": "string", "example": "ABCDE"}}, "required": ["username", "password"]}, "TeacherBasicInfo": {"type": "object", "properties": {"teacherId": {"type": "integer", "description": "老师ID", "example": 1001}, "name": {"type": "string", "description": "老师姓名", "example": "张老师"}, "phone": {"type": "string", "description": "联系电话", "example": "13800138000"}, "courseCount": {"type": "integer", "description": "本周课程数", "example": 12}, "studentCount": {"type": "integer", "description": "关联学生数", "example": 8}}}, "BookingInfo": {"type": "object", "properties": {"bookingId": {"type": "integer", "example": 1001}, "studentId": {"type": "integer", "example": 501}, "studentName": {"type": "string", "example": "张三"}, "requestDate": {"type": "string", "format": "date-time", "example": "2023-10-15T14:30:00Z"}, "courseType": {"type": "string", "example": "数学辅导"}, "status": {"type": "string", "enum": ["pending", "confirmed", "rejected"], "example": "pending"}, "conflictFlag": {"type": "boolean", "example": true}}}, "SalaryPageResult": {"type": "object", "properties": {"total": {"type": "integer", "example": 100}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TeacherSalaryDTO"}}}}, "CourseSchedule": {"type": "object", "properties": {"courseId": {"type": "integer", "description": "课程ID", "example": 1001}, "courseDate": {"type": "string", "format": "date", "description": "上课日期", "example": "2023-05-15"}, "startTime": {"type": "string", "description": "开始时间", "example": "14:00:00"}, "endTime": {"type": "string", "description": "结束时间", "example": "15:30:00"}, "classroom": {"type": "string", "description": "教室名称", "example": "302教室"}, "studentName": {"type": "string", "description": "学生姓名", "example": "张三"}, "gradeLevel": {"type": "string", "description": "年级", "example": "五年级"}, "courseContent": {"type": "string", "description": "课程内容", "example": "数学-几何基础知识"}, "reminderSent": {"type": "boolean", "description": "是否已发送提醒", "example": true}}}, "SalarySummary": {"type": "object", "properties": {"salaryId": {"type": "integer", "example": 1001}, "period": {"type": "string", "format": "date", "example": "2023-08-01"}, "totalAmount": {"type": "number", "format": "float", "example": 8650}, "status": {"type": "string", "enum": ["pending", "paid"], "example": "paid"}}}, "SchoolInfo": {"type": "object", "properties": {"school_id": {"type": "integer", "example": 1001}, "name": {"type": "string", "example": "希望小学"}, "address": {"type": "string", "example": "北京市海淀区"}, "admin_quota": {"type": "integer", "example": 3}, "status": {"type": "string", "example": "active"}, "created_at": {"type": "string", "format": "date-time"}, "updated_at": {"type": "string", "format": "date-time"}}}, "PrincipalInfo": {"type": "object", "properties": {"userId": {"type": "integer", "example": 1001}, "username": {"type": "string", "example": "principal_zhang"}, "realName": {"type": "string", "example": "张校长"}, "phone": {"type": "string", "example": "13800138000"}, "schoolId": {"type": "integer", "example": 2001}, "schoolName": {"type": "string", "example": "第一中学"}, "mfaBound": {"type": "boolean", "example": true}, "createdAt": {"type": "string", "format": "date-time", "example": "2023-06-15T10:30:00Z"}}}, "UserInfo": {"type": "object", "properties": {"userId": {"type": "integer", "example": 10001}, "username": {"type": "string", "example": "<PERSON><PERSON><PERSON>"}, "realName": {"type": "string", "example": "张三"}, "role": {"type": "string", "example": "teacher"}, "schoolId": {"type": "integer", "example": 201}, "schoolName": {"type": "string", "example": "育才中学"}, "phone": {"type": "string", "example": "13800138000"}, "createdAt": {"type": "string", "format": "date-time", "example": "2023-05-10T08:30:00Z"}}}, "LoginResponse": {"type": "object", "properties": {"userId": {"type": "integer", "example": 1}, "username": {"type": "string", "example": "admin"}, "realName": {"type": "string", "example": "管理员"}, "role": {"type": "string", "enum": ["super_admin", "principal", "teacher"], "example": "super_admin"}, "schoolId": {"type": "integer", "example": 1}, "token": {"type": "string", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}}, "TeacherDetailInfo": {"type": "object", "properties": {"teacherId": {"type": "integer", "example": 1001}, "realName": {"type": "string", "example": "张三"}, "phone": {"type": "string", "example": "13800138000"}, "students": {"type": "array", "description": "关联学生列表", "items": {"$ref": "#/components/schemas/TeacherStudentInfo"}}, "salaryInfo": {"$ref": "#/components/schemas/TeacherSalaryInfo"}}}, "HandleBookingRequest": {"type": "object", "properties": {"bookingId": {"type": "integer", "example": 1001}, "action": {"type": "string", "enum": ["confirm", "reject"], "example": "confirm"}, "adjustedTime": {"type": "string", "format": "date-time", "example": "2023-10-16T15:00:00Z"}, "note": {"type": "string", "example": "时间调整到次日下午"}}, "required": ["bookingId", "action"]}, "TeacherSalaryDTO": {"type": "object", "properties": {"salaryId": {"type": "integer", "example": 1001}, "teacherId": {"type": "integer", "example": 2001}, "teacherName": {"type": "string", "example": "张老师"}, "period": {"type": "string", "format": "date", "example": "2023-10"}, "baseAmount": {"type": "number", "format": "decimal", "example": 8000}, "bonus": {"type": "number", "format": "decimal", "example": 1200.5}, "deductions": {"type": "number", "format": "decimal", "example": 300}, "totalAmount": {"type": "number", "format": "decimal", "example": 8900.5}, "courseHours": {"type": "integer", "example": 40}}}, "AttendanceRecord": {"type": "object", "properties": {"courseDate": {"type": "string", "format": "date", "example": "2025-07-20"}, "courseTime": {"type": "string", "example": "14:00-15:30"}, "subject": {"type": "string", "example": "物理"}, "status": {"type": "string", "enum": ["present", "absent", "late"], "example": "present"}, "recordedAt": {"type": "string", "format": "date-time", "example": "2025-07-20T15:35:00Z"}}}, "SalaryDetail": {"type": "object", "properties": {"salaryId": {"type": "integer", "example": 1001}, "period": {"type": "string", "example": "2023年8月"}, "baseAmount": {"type": "number", "example": 8000}, "bonus": {"type": "number", "example": 1000}, "deductions": {"type": "number", "example": 350}, "totalAmount": {"type": "number", "example": 8650}, "paymentDate": {"type": "string", "format": "date", "example": "2023-09-05"}, "breakdown": {"type": "array", "items": {"type": "object", "properties": {"courseDate": {"type": "string", "format": "date"}, "studentName": {"type": "string"}, "duration": {"type": "number"}, "amount": {"type": "number"}}}}}}, "CreateSchoolRequest": {"type": "object", "required": ["name"], "properties": {"name": {"type": "string", "example": "新建学校"}, "address": {"type": "string", "example": "上海市浦东新区"}, "admin_quota": {"type": "integer", "example": 2}, "status": {"type": "string", "enum": ["active", "inactive"], "default": "active"}}}, "CreatePrincipalRequest": {"type": "object", "required": ["username", "realName", "schoolId", "phone"], "properties": {"username": {"type": "string", "example": "new_principal"}, "password": {"type": "string", "example": "Init@123"}, "realName": {"type": "string", "example": "李校长"}, "schoolId": {"type": "integer", "example": 2001}, "phone": {"type": "string", "example": "13900139000"}}}, "UserDetail": {"allOf": [{"$ref": "#/components/schemas/UserInfo"}, {"type": "object", "properties": {"lastLoginAt": {"type": "string", "format": "date-time", "example": "2024-01-15T14:20:00Z"}, "mfaBound": {"type": "boolean", "example": true}}}]}, "UpdatePasswordRequest": {"type": "object", "properties": {"oldPassword": {"type": "string", "example": "oldPassword"}, "newPassword": {"type": "string", "example": "newPassword"}}, "required": ["oldPassword", "newPassword"]}, "TeacherStudentInfo": {"type": "object", "properties": {"studentId": {"type": "integer", "example": 2001}, "name": {"type": "string", "example": "<PERSON>"}, "className": {"type": "string", "example": "三年级二班"}, "lastAttend": {"type": "string", "enum": ["present", "absent", "late"], "description": "最近考勤状态", "example": "present"}}}, "ConflictSolution": {"type": "object", "properties": {"solutionId": {"type": "integer", "example": 1}, "description": {"type": "string", "example": "调整至次日下午3点"}, "teacherId": {"type": "integer", "example": 201}, "teacherName": {"type": "string", "example": "李老师"}, "classroomId": {"type": "integer", "example": 301}, "classroomName": {"type": "string", "example": "301教室"}, "adjustedTime": {"type": "string", "format": "date-time", "example": "2023-10-16T15:00:00Z"}}}, "UnpaidStudentList": {"type": "array", "items": {"$ref": "#/components/schemas/UnpaidStudentDTO"}}, "AttendanceHistory": {"type": "object", "properties": {"attendanceId": {"type": "integer", "description": "考勤记录ID", "example": 3001}, "courseDate": {"type": "string", "format": "date", "example": "2023-05-15"}, "startTime": {"type": "string", "example": "14:00:00"}, "studentName": {"type": "string", "example": "张三"}, "status": {"type": "string", "example": "present"}, "recordedAt": {"type": "string", "format": "date-time", "example": "2023-05-15T14:05:00Z"}, "note": {"type": "string", "example": "学生认真听讲"}}}, "RadarDataPoint": {"type": "object", "properties": {"dimension": {"type": "string", "example": "代数"}, "score": {"type": "number", "format": "float", "example": 92}}}, "MessageSummary": {"type": "object", "properties": {"messageId": {"type": "integer", "example": 2005}, "title": {"type": "string", "example": "新课程安排通知"}, "senderType": {"type": "string", "enum": ["system", "principal", "parent"], "example": "principal"}, "senderName": {"type": "string", "example": "张校长"}, "messageType": {"type": "string", "enum": ["booking", "notification", "payment"], "example": "notification"}, "priority": {"type": "string", "enum": ["normal", "urgent"], "example": "urgent"}, "status": {"type": "string", "enum": ["unread", "read", "confirmed"], "example": "unread"}, "createdAt": {"type": "string", "format": "date-time", "example": "2023-08-15T14:30:00Z"}}}, "UpdateSchoolRequest": {"type": "object", "properties": {"name": {"type": "string", "example": "更新后的校名"}, "address": {"type": "string", "example": "广州市天河区"}, "admin_quota": {"type": "integer", "example": 5}}}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "请求参数无效"}, "errors": {"type": "array", "items": {"type": "string"}, "example": ["学校ID不能为空"]}}}, "CreateUserRequest": {"type": "object", "required": ["username", "realName", "role", "schoolId"], "properties": {"username": {"type": "string", "example": "lisi_edu"}, "password": {"type": "string", "example": "Init@123"}, "realName": {"type": "string", "example": "李四"}, "role": {"type": "string", "enum": ["super_admin", "principal", "teacher"], "example": "teacher"}, "schoolId": {"type": "integer", "example": 201}, "phone": {"type": "string", "example": "13900139000"}}}, "UserProfile": {"type": "object", "properties": {"userId": {"type": "integer", "example": 1}, "username": {"type": "string", "example": "admin"}, "realName": {"type": "string", "example": "管理员"}, "role": {"type": "string", "enum": ["super_admin", "principal", "teacher"], "example": "super_admin"}, "phone": {"type": "string", "example": "13800138000"}, "schoolId": {"type": "integer", "example": 1}, "schoolName": {"type": "string", "example": "示范学校"}}}, "TeacherSalaryInfo": {"type": "object", "properties": {"period": {"type": "string", "format": "date", "description": "薪资周期", "example": "2024-06"}, "baseAmount": {"type": "number", "format": "double", "example": 4500}, "bonus": {"type": "number", "format": "double", "example": 800}, "totalAmount": {"type": "number", "format": "double", "example": 5300}}}, "MessageInfo": {"type": "object", "properties": {"messageId": {"type": "integer", "example": 7001}, "type": {"type": "string", "enum": ["booking", "notification", "payment"], "example": "booking"}, "title": {"type": "string", "example": "新的约课请求"}, "content": {"type": "string", "example": "学生张三请求10月15日数学辅导"}, "sender": {"type": "string", "example": "系统"}, "priority": {"type": "string", "enum": ["normal", "urgent"], "example": "normal"}, "status": {"type": "string", "enum": ["unread", "read", "confirmed"], "example": "unread"}, "createTime": {"type": "string", "format": "date-time", "example": "2023-10-10T09:15:00Z"}}}, "UnpaidStudentDTO": {"type": "object", "properties": {"studentId": {"type": "integer", "example": 3001}, "studentName": {"type": "string", "example": "<PERSON>"}, "className": {"type": "string", "example": "三年二班"}, "contactPhone": {"type": "string", "example": "13800138000"}, "unpaidAmount": {"type": "number", "format": "decimal", "example": 2500}, "lastPaymentDate": {"type": "string", "format": "date", "example": "2023-09-15"}}}, "AttendanceStats": {"type": "object", "properties": {"totalClasses": {"type": "integer", "example": 20}, "present": {"type": "integer", "example": 18}, "absent": {"type": "integer", "example": 1}, "late": {"type": "integer", "example": 1}, "rate": {"type": "number", "format": "float", "example": 95}}}, "MessageDetail": {"type": "object", "properties": {"messageId": {"type": "integer", "example": 2005}, "title": {"type": "string", "example": "新课程安排通知"}, "content": {"type": "string", "example": "您下周新增周三下午3点的数学辅导课程，学生：李小明"}, "sender": {"type": "object", "properties": {"userId": {"type": "integer"}, "name": {"type": "string"}, "role": {"type": "string"}}}, "attachments": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string"}}}}, "requiresConfirm": {"type": "boolean", "example": true}, "createdAt": {"type": "string", "format": "date-time"}}}, "UpdateUserRequest": {"type": "object", "properties": {"realName": {"type": "string", "example": "李四(修改)"}, "phone": {"type": "string", "example": "13900139222"}, "schoolId": {"type": "integer", "example": 202}, "status": {"type": "string", "enum": ["active", "inactive"], "example": "active"}}}, "UserSettings": {"type": "object", "properties": {"notifyCourse": {"type": "boolean", "description": "课程提醒", "example": true}, "notifyPayment": {"type": "boolean", "description": "缴费提醒", "example": true}, "notifySystem": {"type": "boolean", "description": "系统通知", "example": true}, "theme": {"type": "string", "enum": ["light", "dark"], "description": "主题设置", "example": "light"}}}, "StudentBasicInfo": {"type": "object", "properties": {"studentId": {"type": "integer", "example": 1001}, "name": {"type": "string", "example": "张三"}, "className": {"type": "string", "example": "初二(3)班"}, "contactPhone": {"type": "string", "example": "13800138000"}, "attendanceRate": {"type": "number", "format": "float", "description": "本月出勤率", "example": 92.5}}}, "UrgentMessageRequest": {"type": "object", "properties": {"receiverType": {"type": "string", "enum": ["teacher", "all", "class"], "example": "teacher"}, "receiverId": {"type": "integer", "example": 201}, "title": {"type": "string", "example": "紧急会议通知"}, "content": {"type": "string", "example": "今天下午4点所有老师到会议室开会"}}, "required": ["receiverType", "content"]}, "StudentPaymentHistory": {"type": "object", "properties": {"studentId": {"type": "integer", "example": 3001}, "studentName": {"type": "string", "example": "<PERSON>"}, "paymentByYear": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentYearSummary"}}}}, "TeacherProfile": {"type": "object", "properties": {"userId": {"type": "integer", "example": 50001}, "realName": {"type": "string", "example": "王老师"}, "phone": {"type": "string", "example": "13800138000"}, "teachingSubject": {"type": "string", "example": "数学"}, "totalStudents": {"type": "integer", "example": 15}, "monthlyCourses": {"type": "integer", "example": 40}}}, "OperationLog": {"type": "object", "properties": {"logId": {"type": "integer", "example": 1}, "operation": {"type": "string", "example": "用户登录"}, "ipAddress": {"type": "string", "example": "***********"}, "result": {"type": "string", "enum": ["success", "failure"], "example": "success"}, "details": {"type": "string", "example": "登录成功"}, "createdAt": {"type": "string", "format": "date-time", "example": "2023-08-01T10:30:00Z"}}}, "StudentDetailInfo": {"type": "object", "properties": {"basicInfo": {"$ref": "#/components/schemas/StudentBasicInfo"}, "learningProgress": {"type": "object", "properties": {"subjects": {"type": "array", "items": {"type": "object", "properties": {"subject": {"type": "string", "example": "数学"}, "progress": {"type": "number", "format": "float", "example": 85}}}}, "radarChart": {"type": "array", "items": {"$ref": "#/components/schemas/RadarDataPoint"}}}, "description": "学习进度雷达图数据"}, "lastUpdate": {"type": "string", "format": "date-time", "example": "2025-07-28T10:30:00Z"}}}, "PaymentYearSummary": {"type": "object", "properties": {"schoolYear": {"type": "string", "example": "2023-2024"}, "totalAmount": {"type": "number", "format": "decimal", "example": 6000}, "paymentDetails": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentRecordDTO"}}}}, "ContactInfo": {"type": "object", "properties": {"studentPhone": {"type": "string", "example": "13800138000"}, "parentPhone": {"type": "string", "example": "13900139000"}, "ctiEnabled": {"type": "boolean", "description": "是否支持CTI一键拨打", "example": true}, "lastContactTime": {"type": "string", "format": "date-time", "example": "2025-07-15T16:20:00Z"}}}, "TeacherProfileUpdate": {"type": "object", "properties": {"phone": {"type": "string", "example": "13800138888"}, "password": {"type": "string", "example": "newPassword123"}, "notificationPreference": {"type": "object", "properties": {"sms": {"type": "boolean"}, "appPush": {"type": "boolean"}, "email": {"type": "boolean"}}}}}, "MenuItem": {"type": "object", "properties": {"name": {"type": "string", "example": "教学管理"}, "path": {"type": "string", "example": "/teaching"}, "icon": {"type": "string", "example": "el-icon-notebook-2"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/SubMenuItem"}}}}, "PaymentRecordDTO": {"type": "object", "properties": {"paymentId": {"type": "integer", "example": 5001}, "amount": {"type": "number", "format": "decimal", "example": 2000}, "paymentDate": {"type": "string", "format": "date", "example": "2023-09-15"}, "createdByName": {"type": "string", "example": "财务处"}}}, "StudentSearchResult": {"type": "object", "properties": {"studentId": {"type": "integer", "example": 1002}, "name": {"type": "string", "example": "李四"}, "className": {"type": "string", "example": "初三(1)班"}, "contactPhone": {"type": "string", "example": "13700137000"}, "latestCourse": {"type": "string", "example": "2025-07-29 15:00 英语"}}}, "SubMenuItem": {"type": "object", "properties": {"name": {"type": "string", "example": "排课管理"}, "path": {"type": "string", "example": "/teaching/schedule"}, "permission": {"type": "string", "example": "teaching:schedule"}}}, "UnpaidExportRequest": {"type": "object", "properties": {"minAmount": {"type": "number", "format": "decimal", "example": 100}, "maxAmount": {"type": "number", "format": "decimal", "example": 5000}, "classIds": {"type": "array", "items": {"type": "integer"}, "example": [101, 102]}}}, "Unauthorized": {"type": "object", "properties": {"code": {"type": "integer", "example": 401}, "message": {"type": "string", "example": "身份认证失败"}}}, "Forbidden": {"type": "object", "properties": {"code": {"type": "integer", "example": 403}, "message": {"type": "string", "example": "没有操作权限"}}}, "BadRequest": {"$ref": "#/components/schemas/ErrorResponse"}, "NotFound": {"type": "object", "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "校长账号不存在"}}}}, "securitySchemes": {}}, "servers": [], "security": []}