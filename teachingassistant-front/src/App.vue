<template>
  <div id="app" :data-theme="appStore.theme">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { useAppStore } from '@/stores/app'

const appStore = useAppStore()

// 监听主题变化，更新document的data-theme属性
watch(() => appStore.theme, (newTheme) => {
  document.documentElement.setAttribute('data-theme', newTheme)
  document.body.setAttribute('data-theme', newTheme)
}, { immediate: true })

onMounted(() => {
  // 初始化应用设置
  appStore.initAppSettings()
  // 立即应用主题
  document.documentElement.setAttribute('data-theme', appStore.theme)
  document.body.setAttribute('data-theme', appStore.theme)
})
</script>

<style>
#app {
  width: 100%;
  height: 100vh;
  background: var(--color-background);
  color: var(--color-text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 全局样式已在 styles/index.css 中定义 */
</style>
