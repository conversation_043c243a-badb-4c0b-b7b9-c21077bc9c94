<template>
  <el-select
    v-model="selectedSubjects"
    placeholder="请选择主教科目（可多选）"
    style="width: 100%"
    multiple
    collapse-tags
    collapse-tags-tooltip
    @change="handleChange"
  >
    <el-option
      v-for="subject in availableSubjects"
      :key="subject"
      :label="subject"
      :value="subject"
    />
  </el-select>
</template>

<script setup>
import { ref, watch } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// 可选科目列表
const availableSubjects = [
  '语文', '数学', '英语', '物理', '化学', '生物',
  '历史', '地理', '政治'
]

// 选中的科目
const selectedSubjects = ref([...props.modelValue])

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  selectedSubjects.value = [...newValue]
}, { deep: true })

// 处理选择变化
const handleChange = (value) => {
  emit('update:modelValue', value)
}
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
