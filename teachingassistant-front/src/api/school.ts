import { request } from '@/utils/request'
import type { ApiResponse, PageResult } from '@/types'

// 学校相关类型定义
export interface SchoolListParams {
  page?: number
  size?: number
  name?: string
  status?: string
}

export interface CreateSchoolData {
  name: string
  address?: string
  phone?: string
  email?: string
  adminQuota?: number
  status?: 'active' | 'inactive'
}

export interface UpdateSchoolData {
  name?: string
  address?: string
  phone?: string
  email?: string
  adminQuota?: number
  status?: 'active' | 'inactive'
}

export interface SchoolInfo {
  schoolId: number
  name: string
  address?: string
  phone?: string
  email?: string
  adminQuota?: number
  status?: string
  createdAt?: string
  updatedAt?: string
}

/**
 * 学校管理API
 */
export const schoolApi = {
  /**
   * 获取学校列表（分页）
   */
  getList(params: SchoolListParams): Promise<ApiResponse<PageResult<SchoolInfo>>> {
    return request.get('/admin/schools', params)
  },

  /**
   * 根据ID获取学校详情
   */
  getById(schoolId: number): Promise<ApiResponse<SchoolInfo>> {
    return request.get(`/admin/schools/${schoolId}`)
  },

  /**
   * 创建学校
   */
  create(data: CreateSchoolData): Promise<ApiResponse<SchoolInfo>> {
    return request.post('/admin/schools', data)
  },

  /**
   * 更新学校信息
   */
  update(schoolId: number, data: UpdateSchoolData): Promise<ApiResponse<SchoolInfo>> {
    return request.put(`/admin/schools/${schoolId}`, data)
  },

  /**
   * 删除学校
   */
  delete(schoolId: number): Promise<ApiResponse<void>> {
    return request.delete(`/admin/schools/${schoolId}`)
  },

  /**
   * 获取所有学校列表（用于下拉选择）
   */
  getAll(): Promise<ApiResponse<SchoolInfo[]>> {
    return request.get('/admin/schools/all')
  }
}
