import { request } from '@/utils/request'
import type { ApiResponse, PageResult } from '@/types'

// 校长管理相关类型定义
export interface PrincipalListParams {
  page?: number
  size?: number
  schoolId?: number
  realName?: string
}

export interface CreatePrincipalData {
  username: string
  password: string
  realName: string
  role: string
  schoolId: number
  phone: string
  email?: string
  gender?: 'M' | 'F'
  hireDate?: string
  subject?: string
  experience?: number
  bio?: string
}

export interface UpdatePrincipalData {
  realName?: string
  phone?: string
  email?: string
  gender?: 'M' | 'F'
  schoolId?: number
  hireDate?: string
  subject?: string
  experience?: number
  bio?: string
  status?: 'active' | 'inactive'
}

export interface PrincipalInfo {
  userId: number
  username: string
  realName: string
  role: string
  schoolId?: number
  schoolName?: string
  phone?: string
  email?: string
  gender?: 'M' | 'F'
  hireDate?: string
  subject?: string
  experience?: number
  bio?: string
  status?: string
  lastLoginAt?: string
  createdAt?: string
  updatedAt?: string
}

/**
 * 校长管理API
 */
export const principalApi = {
  /**
   * 获取校长列表（分页）
   */
  getList(params: PrincipalListParams): Promise<ApiResponse<PageResult<PrincipalInfo>>> {
    return request.get('/admin/principals', params)
  },

  /**
   * 根据ID获取校长详情
   */
  getById(userId: number): Promise<ApiResponse<PrincipalInfo>> {
    return request.get(`/admin/users/${userId}`)
  },

  /**
   * 创建校长
   */
  create(data: CreatePrincipalData): Promise<ApiResponse<PrincipalInfo>> {
    return request.post('/admin/principals', data)
  },

  /**
   * 更新校长信息
   */
  update(userId: number, data: UpdatePrincipalData): Promise<ApiResponse<PrincipalInfo>> {
    return request.put(`/admin/principals/${userId}`, data)
  },

  /**
   * 删除校长
   */
  delete(userId: number): Promise<ApiResponse<void>> {
    return request.delete(`/admin/principals/${userId}`)
  },

  /**
   * 重置校长密码
   */
  resetPassword(userId: number, newPassword: string): Promise<ApiResponse<void>> {
    return request.put(`/admin/users/${userId}/password`, { newPassword })
  },

  /**
   * 更新校长状态
   */
  updateStatus(userId: number, status: 'active' | 'inactive'): Promise<ApiResponse<void>> {
    return request.put(`/admin/users/${userId}/status?status=${status}`)
  }
}
