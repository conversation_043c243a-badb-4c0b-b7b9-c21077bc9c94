import { request } from '@/utils/request'
import type { ApiResponse, PageResult, Classroom } from '@/types'

// 教室管理相关类型定义
export interface ClassroomListParams {
  page?: number
  size?: number
  schoolId?: number
  name?: string
  floor?: number
  type?: 'normal' | 'multimedia' | 'lab'
  status?: 'available' | 'in_use' | 'maintenance'
}

export interface CreateClassroomData {
  schoolId?: number  // 管理员端需要，校长端自动设置
  name: string
  floor?: number
  type?: 'normal' | 'multimedia' | 'lab'
  capacity?: number
  equipment?: string
  status?: 'available' | 'in_use' | 'maintenance'
}

export interface UpdateClassroomData {
  name?: string
  floor?: number
  type?: 'normal' | 'multimedia' | 'lab'
  capacity?: number
  equipment?: string
  status?: 'available' | 'in_use' | 'maintenance'
}

/**
 * 管理员端教室管理API
 */
export const adminClassroomApi = {
  /**
   * 获取教室列表（分页）
   */
  getList(params: ClassroomListParams): Promise<ApiResponse<PageResult<Classroom>>> {
    return request.get('/admin/classrooms', params)
  },

  /**
   * 获取所有教室列表（不分页）
   */
  getAll(): Promise<ApiResponse<Classroom[]>> {
    return request.get('/admin/classrooms/all')
  },

  /**
   * 根据ID获取教室详情
   */
  getById(classroomId: number): Promise<ApiResponse<Classroom>> {
    return request.get(`/admin/classrooms/${classroomId}`)
  },

  /**
   * 创建教室
   */
  create(data: CreateClassroomData): Promise<ApiResponse<Classroom>> {
    return request.post('/admin/classrooms', data)
  },

  /**
   * 更新教室信息
   */
  update(classroomId: number, data: UpdateClassroomData): Promise<ApiResponse<Classroom>> {
    return request.put(`/admin/classrooms/${classroomId}`, data)
  },

  /**
   * 删除教室
   */
  delete(classroomId: number): Promise<ApiResponse<void>> {
    return request.delete(`/admin/classrooms/${classroomId}`)
  }
}

/**
 * 校长端教室管理API
 */
export const principalClassroomApi = {
  /**
   * 获取教室列表（分页）
   */
  getList(params: Omit<ClassroomListParams, 'schoolId'>): Promise<ApiResponse<PageResult<Classroom>>> {
    return request.get('/principal/classrooms', params)
  },

  /**
   * 获取所有教室列表（不分页）
   */
  getAll(): Promise<ApiResponse<Classroom[]>> {
    return request.get('/principal/classrooms/all')
  },

  /**
   * 根据ID获取教室详情
   */
  getById(classroomId: number): Promise<ApiResponse<Classroom>> {
    return request.get(`/principal/classrooms/${classroomId}`)
  },

  /**
   * 创建教室
   */
  create(data: Omit<CreateClassroomData, 'schoolId'>): Promise<ApiResponse<Classroom>> {
    return request.post('/principal/classrooms', data)
  },

  /**
   * 更新教室信息
   */
  update(classroomId: number, data: UpdateClassroomData): Promise<ApiResponse<Classroom>> {
    return request.put(`/principal/classrooms/${classroomId}`, data)
  },

  /**
   * 删除教室
   */
  delete(classroomId: number): Promise<ApiResponse<void>> {
    return request.delete(`/principal/classrooms/${classroomId}`)
  }
}

/**
 * 教室类型选项
 */
export const classroomTypeOptions = [
  { label: '普通教室', value: 'normal' },
  { label: '多媒体教室', value: 'multimedia' },
  { label: '实验室', value: 'lab' }
]

/**
 * 教室状态选项
 */
export const classroomStatusOptions = [
  { label: '可用', value: 'available' },
  { label: '使用中', value: 'in_use' },
  { label: '维护中', value: 'maintenance' }
]

/**
 * 获取教室类型文本
 */
export const getClassroomTypeText = (type: string): string => {
  const option = classroomTypeOptions.find(item => item.value === type)
  return option ? option.label : type
}

/**
 * 获取教室状态文本
 */
export const getClassroomStatusText = (status: string): string => {
  const option = classroomStatusOptions.find(item => item.value === status)
  return option ? option.label : status
}

/**
 * 获取教室类型标签类型
 */
export const getClassroomTypeTagType = (type: string): string => {
  switch (type) {
    case 'normal':
      return ''
    case 'multimedia':
      return 'success'
    case 'lab':
      return 'warning'
    default:
      return ''
  }
}

/**
 * 获取教室状态标签类型
 */
export const getClassroomStatusTagType = (status: string): string => {
  switch (status) {
    case 'available':
      return 'success'
    case 'in_use':
      return 'warning'
    case 'maintenance':
      return 'danger'
    default:
      return ''
  }
}
