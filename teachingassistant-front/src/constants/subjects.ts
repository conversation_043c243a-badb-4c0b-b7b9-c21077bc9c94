/**
 * 科目相关常量
 */

// 可选科目列表
export const AVAILABLE_SUBJECTS = [
  '语文',
  '数学', 
  '英语',
  '物理',
  '化学',
  '生物',
  '历史',
  '地理',
  '政治',
] as const

// 科目类型
export type Subject = typeof AVAILABLE_SUBJECTS[number]

// 科目分类
export const SUBJECT_CATEGORIES = {
  language: ['语文', '英语'],
  science: ['数学', '物理', '化学', '生物'],
  humanities: ['历史', '地理', '政治'],
  arts: ['音乐', '美术'],
  physical: ['体育'],
  technology: ['信息技术', '通用技术']
} as const

// 获取科目分类
export const getSubjectCategory = (subject: string): string => {
  for (const [category, subjects] of Object.entries(SUBJECT_CATEGORIES)) {
    if (subjects.includes(subject as any)) {
      return category
    }
  }
  return 'other'
}

// 验证科目是否有效
export const isValidSubject = (subject: string): boolean => {
  return AVAILABLE_SUBJECTS.includes(subject as Subject)
}

// 验证科目数组是否都有效
export const areValidSubjects = (subjects: string[]): boolean => {
  return subjects.every(subject => isValidSubject(subject))
}

// 将科目数组转换为显示字符串
export const subjectsToString = (subjects: string[]): string => {
  return subjects.join('、')
}

// 将逗号分隔的字符串转换为科目数组
export const stringToSubjects = (subjectString: string): string[] => {
  if (!subjectString || subjectString.trim() === '') {
    return []
  }
  return subjectString.split(',').map(s => s.trim()).filter(s => s !== '')
}
