<template>
  <div class="settings-container">
    <div class="header">
      <h2>系统设置</h2>
    </div>

    <div class="settings-content">
      <el-tabs v-model="activeTab" tab-position="left">
        <el-tab-pane label="通知设置" name="notification">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>消息提醒设置</span>
              </div>
            </template>
            
            <div class="setting-section">
              <!--<div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">邮件通知</div>
                  <div class="setting-desc">接收系统重要消息的邮件通知</div>
                </div>
                <el-switch v-model="settings.emailNotification" />
              </div>-->
              
              <!--<div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">短信通知</div>
                  <div class="setting-desc">接收紧急消息的短信通知</div>
                </div>
                <el-switch v-model="settings.smsNotification" />
              </div>-->
              
              <!--<div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">浏览器通知</div>
                  <div class="setting-desc">在浏览器中显示桌面通知</div>
                </div>
                <el-switch v-model="settings.browserNotification" />
              </div>-->
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">课程提醒</div>
                  <div class="setting-desc">上课前提醒通知</div>
                </div>
                <el-switch v-model="settings.classReminder" />
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">约课提醒</div>
                  <div class="setting-desc">有新的未查看的约课提醒</div>
                </div>
                <el-switch v-model="settings.appointmentReminder" />
              </div>
            </div>
          </el-card>
        </el-tab-pane>

        <!--<el-tab-pane label="隐私设置" name="privacy">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>隐私保护设置</span>
              </div>
            </template>
            
            <div class="setting-section">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">个人信息可见性</div>
                  <div class="setting-desc">控制其他用户查看您的个人信息</div>
                </div>
                <el-select v-model="settings.profileVisibility" placeholder="请选择">
                  <el-option label="公开" value="public" />
                  <el-option label="仅同事" value="colleagues" />
                  <el-option label="仅管理员" value="admin" />
                </el-select>
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">联系方式可见性</div>
                  <div class="setting-desc">控制其他用户查看您的联系方式</div>
                </div>
                <el-select v-model="settings.contactVisibility" placeholder="请选择">
                  <el-option label="公开" value="public" />
                  <el-option label="仅同事" value="colleagues" />
                  <el-option label="不公开" value="private" />
                </el-select>
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">在线状态显示</div>
                  <div class="setting-desc">显示您的在线状态给其他用户</div>
                </div>
                <el-switch v-model="settings.showOnlineStatus" />
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">活动记录</div>
                  <div class="setting-desc">记录您的系统使用活动</div>
                </div>
                <el-switch v-model="settings.activityLogging" />
              </div>
            </div>
          </el-card>
        </el-tab-pane>-->

        <el-tab-pane label="界面设置" name="interface">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>界面个性化设置</span>
              </div>
            </template>
            
            <div class="setting-section">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">主题模式</div>
                  <div class="setting-desc">选择您喜欢的界面主题，实时预览效果</div>
                </div>
                <el-radio-group v-model="settings.theme" @change="handleThemeChange">
                  <el-radio label="light">
                    <div class="theme-option">
                      <div class="theme-preview light-preview">
                        <div class="preview-header"></div>
                        <div class="preview-sidebar"></div>
                        <div class="preview-content"></div>
                      </div>
                      <span>浅色模式</span>
                    </div>
                  </el-radio>
                  <el-radio label="dark">
                    <div class="theme-option">
                      <div class="theme-preview dark-preview">
                        <div class="preview-header"></div>
                        <div class="preview-sidebar"></div>
                        <div class="preview-content"></div>
                      </div>
                      <span>深色模式</span>
                    </div>
                  </el-radio>
                </el-radio-group>
              </div>
              
              <!--<div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">语言设置</div>
                  <div class="setting-desc">选择系统显示语言</div>
                </div>
                <el-select v-model="settings.language" placeholder="请选择语言">
                  <el-option label="简体中文" value="zh-CN" />
                  <el-option label="繁體中文" value="zh-TW" />
                  <el-option label="English" value="en-US" />
                </el-select>
              </div>-->
              
              <!--<div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">字体大小</div>
                  <div class="setting-desc">调整系统字体大小</div>
                </div>
                <el-radio-group v-model="settings.fontSize">
                  <el-radio label="small">小</el-radio>
                  <el-radio label="medium">中</el-radio>
                  <el-radio label="large">大</el-radio>
                </el-radio-group>
              </div>-->
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">侧边栏折叠</div>
                  <div class="setting-desc">默认折叠侧边栏导航</div>
                </div>
                <el-switch v-model="settings.sidebarCollapsed" @change="handleSidebarChange" />
              </div>
              
              <!--<div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">表格密度</div>
                  <div class="setting-desc">调整表格行间距</div>
                </div>
                <el-radio-group v-model="settings.tableDensity">
                  <el-radio label="compact">紧凑</el-radio>
                  <el-radio label="default">默认</el-radio>
                  <el-radio label="comfortable">舒适</el-radio>
                </el-radio-group>
              </div>-->
            </div>
          </el-card>
        </el-tab-pane>

        <!--<el-tab-pane label="安全设置" name="security">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>账户安全设置</span>
              </div>
            </template>
            
            <div class="setting-section">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">自动登出</div>
                  <div class="setting-desc">无操作时自动登出的时间</div>
                </div>
                <el-select v-model="settings.autoLogout" placeholder="请选择">
                  <el-option label="30分钟" value="30" />
                  <el-option label="1小时" value="60" />
                  <el-option label="2小时" value="120" />
                  <el-option label="4小时" value="240" />
                  <el-option label="永不" value="0" />
                </el-select>
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">登录验证</div>
                  <div class="setting-desc">启用双因素身份验证</div>
                </div>
                <el-switch v-model="settings.twoFactorAuth" />
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">登录提醒</div>
                  <div class="setting-desc">异地登录时发送提醒</div>
                </div>
                <el-switch v-model="settings.loginAlert" />
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">密码强度检查</div>
                  <div class="setting-desc">修改密码时检查密码强度</div>
                </div>
                <el-switch v-model="settings.passwordStrengthCheck" />
              </div>
            </div>
          </el-card>
        </el-tab-pane>-->

        <!--<el-tab-pane label="数据设置" name="data">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>数据管理设置</span>
              </div>
            </template>
            
            <div class="setting-section">
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">数据备份</div>
                  <div class="setting-desc">定期备份您的个人数据</div>
                </div>
                <el-switch v-model="settings.dataBackup" />
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">数据同步</div>
                  <div class="setting-desc">在多设备间同步数据</div>
                </div>
                <el-switch v-model="settings.dataSync" />
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">缓存清理</div>
                  <div class="setting-desc">定期清理浏览器缓存</div>
                </div>
                <el-button type="primary" @click="handleClearCache">清理缓存</el-button>
              </div>
              
              <div class="setting-item">
                <div class="setting-info">
                  <div class="setting-title">导出数据</div>
                  <div class="setting-desc">导出您的个人数据</div>
                </div>
                <el-button type="primary" @click="handleExportData">导出数据</el-button>
              </div>
            </div>
          </el-card>
        </el-tab-pane>-->
      </el-tabs>
    </div>

    <div class="settings-actions">
      <el-button type="primary" @click="handleSave">保存设置</el-button>
      <!--<el-button @click="handleReset">重置为默认</el-button>-->
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAppStore } from '@/stores/app'
import request from '@/utils/request'

// 响应式数据
const activeTab = ref('interface')
const appStore = useAppStore()

// 设置数据
const settings = reactive({
  // 通知设置
  emailNotification: true,
  smsNotification: false,
  browserNotification: true,
  classReminder: true,
  appointmentReminder: true,
  
  // 隐私设置
  profileVisibility: 'colleagues',
  contactVisibility: 'colleagues',
  showOnlineStatus: true,
  activityLogging: true,
  
  // 界面设置
  theme: appStore.theme,
  language: 'zh-CN',
  fontSize: 'medium',
  sidebarCollapsed: appStore.sidebarCollapsed,
  tableDensity: 'default',
  
  // 安全设置
  autoLogout: '60',
  twoFactorAuth: false,
  loginAlert: true,
  passwordStrengthCheck: true,
  
  // 数据设置
  dataBackup: true,
  dataSync: true
})

// 默认设置
const defaultSettings = { ...settings }

// 方法
const fetchSettings = async () => {
  try {
    const response = await request.get('/api/user/settings')
    if (response.data) {
      // 更新设置数据
      Object.assign(settings, {
        notificationEnabled: response.data.notificationEnabled,
        classReminder: response.data.classReminder,
        appointmentReminder: response.data.appointmentReminder,
        theme: response.data.theme,
        language: response.data.language,
        sidebarCollapsed: response.data.sidebarCollapsed
      })

      // 同步到store
      appStore.setTheme(response.data.theme)
      appStore.setSidebarCollapsed(response.data.sidebarCollapsed)
    }
  } catch (error) {
    console.error('获取设置失败:', error)
    ElMessage.error('获取设置失败')
  }
}

// 主题切换处理
const handleThemeChange = (newTheme) => {
  console.log('主题切换:', newTheme)
  appStore.setTheme(newTheme)

  // 立即更新设置中的主题值
  settings.theme = newTheme

  // 显示成功消息
  ElMessage.success(`已切换到${newTheme === 'light' ? '浅色' : '深色'}模式`)

  // 可选：立即保存到后端
  setTimeout(async () => {
    try {
      await request.put('/api/user/settings/theme', { theme: newTheme })
      console.log('主题设置已保存到后端')
    } catch (error) {
      console.error('保存主题设置失败:', error)
    }
  }, 100)
}

// 侧边栏折叠处理
const handleSidebarChange = (collapsed) => {
  appStore.setSidebarCollapsed(collapsed)
}

const handleSave = async () => {
  try {
    // 保存设置到后端
    await request.put('/api/user/settings', {
      notificationEnabled: settings.notificationEnabled,
      classReminder: settings.classReminder,
      appointmentReminder: settings.appointmentReminder,
      theme: settings.theme,
      language: settings.language,
      sidebarCollapsed: settings.sidebarCollapsed
    })

    // 保存主题设置到store
    appStore.setTheme(settings.theme)
    appStore.setSidebarCollapsed(settings.sidebarCollapsed)

    ElMessage.success('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
    ElMessage.error('保存设置失败')
  }
}

const handleReset = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要重置为默认设置吗？这将清除您的所有个性化设置。',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    Object.assign(settings, defaultSettings)
    ElMessage.success('已重置为默认设置')
  } catch (error) {
    // 用户取消
  }
}

const handleClearCache = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理浏览器缓存吗？这可能会影响页面加载速度。',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // TODO: 清理缓存逻辑
    ElMessage.success('缓存清理成功')
  } catch (error) {
    // 用户取消
  }
}

const handleExportData = () => {
  ElMessage.info('数据导出功能待开发')
}

// 监听store中的主题变化
watch(() => appStore.theme, (newTheme) => {
  settings.theme = newTheme
})

// 监听store中的侧边栏状态变化
watch(() => appStore.sidebarCollapsed, (collapsed) => {
  settings.sidebarCollapsed = collapsed
})

// 生命周期
onMounted(() => {
  fetchSettings()
  // 初始化设置
  settings.theme = appStore.theme
  settings.sidebarCollapsed = appStore.sidebarCollapsed
})
</script>

<style scoped>
.settings-container {
  padding: 20px;
}

.header {
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: var(--color-heading);
}

.settings-content {
  background: var(--color-card-background);
  border-radius: 12px;
  box-shadow: 0 8px 32px var(--color-card-shadow);
  min-height: 600px;
  border: 1px solid var(--color-card-border);
}

.card-header {
  font-weight: bold;
  color: var(--color-heading);
}

.setting-section {
  padding: 20px 0;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid var(--color-border);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  flex: 1;
  margin-right: 20px;
}

.setting-title {
  font-weight: bold;
  color: var(--color-heading);
  margin-bottom: 5px;
}

.setting-desc {
  color: var(--color-text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.settings-actions {
  margin-top: 20px;
  text-align: center;
}

.settings-actions .el-button {
  margin: 0 10px;
}

:deep(.el-tabs--left .el-tabs__content) {
  padding-left: 20px;
}

:deep(.el-tabs--left .el-tabs__nav-wrap) {
  margin-right: 20px;
}

:deep(.el-tabs--left .el-tabs__item) {
  text-align: left;
  padding: 0 20px;
}

/* 主题选择样式 */
.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.theme-preview {
  width: 60px;
  height: 40px;
  border-radius: 6px;
  position: relative;
  overflow: hidden;
  border: 2px solid var(--color-border);
  transition: all 0.3s ease;
}

.theme-preview:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px var(--color-card-shadow);
}

.preview-header {
  height: 8px;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.preview-sidebar {
  width: 15px;
  height: 100%;
  position: absolute;
  top: 8px;
  left: 0;
  bottom: 0;
}

.preview-content {
  position: absolute;
  top: 8px;
  left: 15px;
  right: 0;
  bottom: 0;
}

/* 浅色主题预览 */
.light-preview {
  background: #ffffff;
}

.light-preview .preview-header {
  background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
}

.light-preview .preview-sidebar {
  background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
}

.light-preview .preview-content {
  background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
}

/* 深色主题预览 */
.dark-preview {
  background: #1a1a1a;
}

.dark-preview .preview-header {
  background: linear-gradient(90deg, #2d2d2d 0%, #3a3a3a 100%);
}

.dark-preview .preview-sidebar {
  background: linear-gradient(180deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
}

.dark-preview .preview-content {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

/* 选中状态 */
:deep(.el-radio.is-checked) .theme-preview {
  border-color: var(--color-primary);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

:deep(.el-radio__input.is-checked + .el-radio__label) .theme-option {
  color: var(--color-primary);
}

/* 调整单选按钮样式 */
:deep(.el-radio) {
  margin-right: 24px;
  margin-bottom: 16px;
}

:deep(.el-radio__input) {
  display: none;
}

:deep(.el-radio__label) {
  padding-left: 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text) !important;
}

/* 深色主题下的Element Plus组件优化 */
[data-theme="dark"] :deep(.el-tabs__item) {
  color: var(--color-text-secondary) !important;
}

[data-theme="dark"] :deep(.el-tabs__item.is-active) {
  color: var(--color-primary) !important;
}

[data-theme="dark"] :deep(.el-tabs__nav-wrap::after) {
  background-color: var(--color-border) !important;
}

[data-theme="dark"] :deep(.el-tabs__active-bar) {
  background-color: var(--color-primary) !important;
}

[data-theme="dark"] :deep(.el-switch__core) {
  background-color: var(--color-border) !important;
}

[data-theme="dark"] :deep(.el-switch.is-checked .el-switch__core) {
  background-color: var(--color-primary) !important;
}

[data-theme="dark"] :deep(.el-select .el-input__inner) {
  background-color: var(--color-card-background) !important;
  border-color: var(--color-border) !important;
  color: var(--color-text) !important;
}

[data-theme="dark"] :deep(.el-input__inner) {
  background-color: var(--color-card-background) !important;
  border-color: var(--color-border) !important;
  color: var(--color-text) !important;
}

[data-theme="dark"] :deep(.el-input__inner:focus) {
  border-color: var(--color-primary) !important;
}

[data-theme="dark"] :deep(.el-button) {
  background-color: var(--color-card-background) !important;
  border-color: var(--color-border) !important;
  color: var(--color-text) !important;
}

[data-theme="dark"] :deep(.el-button--primary) {
  background-color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
  color: #ffffff !important;
}

[data-theme="dark"] :deep(.el-button:hover) {
  background-color: var(--color-background-soft) !important;
  border-color: var(--color-primary) !important;
  color: var(--color-primary) !important;
}

[data-theme="dark"] :deep(.el-button--primary:hover) {
  background-color: var(--color-primary-light) !important;
  border-color: var(--color-primary-light) !important;
  color: #ffffff !important;
}
</style>
