<template>
  <div class="logs-container">
    <div class="header">
      <h2>操作日志</h2>
      <el-button type="primary" @click="handleExport">
        <el-icon><Download /></el-icon>
        导出日志
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="操作类型">
          <el-select
            v-model="searchForm.action"
            placeholder="请选择操作类型"
            clearable
          >
            <el-option label="登录" value="login" />
            <el-option label="登出" value="logout" />
            <el-option label="查看" value="view" />
            <el-option label="创建" value="create" />
            <el-option label="编辑" value="edit" />
            <el-option label="删除" value="delete" />
            <el-option label="导出" value="export" />
            <el-option label="设置" value="setting" />
          </el-select>
        </el-form-item>
        <el-form-item label="模块">
          <el-select
            v-model="searchForm.module"
            placeholder="请选择模块"
            clearable
          >
            <el-option label="用户管理" value="user" />
            <el-option label="课程管理" value="course" />
            <el-option label="学生管理" value="student" />
            <el-option label="考勤管理" value="attendance" />
            <el-option label="消息中心" value="message" />
            <el-option label="系统设置" value="system" />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ totalLogs }}</div>
                <div class="stats-label">总操作数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon today">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ todayLogs }}</div>
                <div class="stats-label">今日操作</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon login">
                <el-icon><User /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ loginCount }}</div>
                <div class="stats-label">登录次数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon last">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ lastLoginTime }}</div>
                <div class="stats-label">最后登录</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="action" label="操作类型" width="100">
          <template #default="scope">
            <el-tag :type="getActionTagType(scope.row.action)">
              {{ getActionText(scope.row.action) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="module" label="模块" width="120">
          <template #default="scope">
            {{ getModuleText(scope.row.module) }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="操作描述" />
        <el-table-column prop="ip" label="IP地址" width="140" />
        <el-table-column prop="userAgent" label="设备信息" width="200">
          <template #default="scope">
            <el-tooltip :content="scope.row.userAgent" placement="top">
              <span class="user-agent-text">{{ getDeviceInfo(scope.row.userAgent) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'">
              {{ scope.row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="操作时间" width="180" />
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 日志详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="操作日志详情"
      width="600px"
    >
      <div class="log-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="操作ID">{{ currentLog.id }}</el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag :type="getActionTagType(currentLog.action)">
              {{ getActionText(currentLog.action) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="模块">{{ getModuleText(currentLog.module) }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="currentLog.status === 'success' ? 'success' : 'danger'">
              {{ currentLog.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="IP地址">{{ currentLog.ip }}</el-descriptions-item>
          <el-descriptions-item label="操作时间">{{ currentLog.createdAt }}</el-descriptions-item>
          <el-descriptions-item label="操作描述" :span="2">{{ currentLog.description }}</el-descriptions-item>
          <el-descriptions-item label="设备信息" :span="2">{{ currentLog.userAgent }}</el-descriptions-item>
        </el-descriptions>

        <div class="log-extra-info" v-if="currentLog.extraData">
          <h4>详细信息</h4>
          <el-table
            :data="formatExtraData(currentLog.extraData)"
            border
            style="width: 100%"
          >
            <el-table-column prop="key" label="字段" width="150" />
            <el-table-column prop="value" label="值" />
          </el-table>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Document, Calendar, User, Clock } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const detailDialogVisible = ref(false)
const currentLog = ref({})

// 统计数据
const totalLogs = ref(0)
const todayLogs = ref(0)
const loginCount = ref(0)
const lastLoginTime = ref('')

// 搜索表单
const searchForm = reactive({
  action: '',
  module: '',
  dateRange: []
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 方法
const getActionText = (action) => {
  const actionMap = {
    login: '登录',
    logout: '登出',
    view: '查看',
    create: '创建',
    edit: '编辑',
    delete: '删除',
    export: '导出',
    setting: '设置'
  }
  return actionMap[action] || action
}

const getActionTagType = (action) => {
  const typeMap = {
    login: 'success',
    logout: 'info',
    view: 'info',
    create: 'success',
    edit: 'warning',
    delete: 'danger',
    export: 'primary',
    setting: 'warning'
  }
  return typeMap[action] || 'info'
}

const getModuleText = (module) => {
  const moduleMap = {
    user: '用户管理',
    course: '课程管理',
    student: '学生管理',
    attendance: '考勤管理',
    message: '消息中心',
    system: '系统设置'
  }
  return moduleMap[module] || module
}

const getDeviceInfo = (userAgent) => {
  if (!userAgent) return '未知设备'
  
  if (userAgent.includes('Chrome')) return 'Chrome浏览器'
  if (userAgent.includes('Firefox')) return 'Firefox浏览器'
  if (userAgent.includes('Safari')) return 'Safari浏览器'
  if (userAgent.includes('Edge')) return 'Edge浏览器'
  
  return '其他浏览器'
}

const formatExtraData = (extraData) => {
  if (!extraData) return []
  
  return Object.entries(extraData).map(([key, value]) => ({
    key,
    value: typeof value === 'object' ? JSON.stringify(value) : value
  }))
}

const fetchStats = async () => {
  try {
    // TODO: 调用API获取统计数据
    // 模拟数据
    totalLogs.value = 1250
    todayLogs.value = 15
    loginCount.value = 89
    lastLoginTime.value = '10:30'
  } catch (error) {
    ElMessage.error('获取统计数据失败')
  }
}

const fetchData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取操作日志
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        action: 'login',
        module: 'system',
        description: '用户登录系统',
        ip: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        status: 'success',
        createdAt: '2024-01-15 10:30:00',
        extraData: {
          loginMethod: 'password',
          deviceType: 'desktop'
        }
      },
      {
        id: 2,
        action: 'view',
        module: 'student',
        description: '查看学生列表',
        ip: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        status: 'success',
        createdAt: '2024-01-15 10:35:00',
        extraData: {
          pageSize: 10,
          filters: { class: '一年级1班' }
        }
      },
      {
        id: 3,
        action: 'edit',
        module: 'attendance',
        description: '修改考勤记录',
        ip: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        status: 'success',
        createdAt: '2024-01-15 11:00:00',
        extraData: {
          studentId: 1001,
          oldStatus: 'absent',
          newStatus: 'present'
        }
      }
    ]
    pagination.total = 3
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  searchForm.action = ''
  searchForm.module = ''
  searchForm.dateRange = []
  pagination.page = 1
  fetchData()
}

const handleExport = () => {
  ElMessage.info('导出日志功能待开发')
}

const handleViewDetail = (row) => {
  currentLog.value = row
  detailDialogVisible.value = true
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 生命周期
onMounted(() => {
  fetchStats()
  fetchData()
})
</script>

<style scoped>
.logs-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.search-bar {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.total {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.today {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.login {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.last {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.table-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.user-agent-text {
  display: inline-block;
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.log-detail {
  padding: 20px 0;
}

.log-extra-info {
  margin-top: 20px;
}

.log-extra-info h4 {
  margin-bottom: 15px;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
