<template>
  <div class="profile-test-container">
    <div class="header">
      <h2>个人中心功能测试</h2>
      <p>测试修改密码、更换邮箱、更新个人信息等功能</p>
    </div>

    <div class="test-sections">
      <!-- 修改密码测试 -->
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>修改密码测试</span>
            <el-button type="primary" @click="testChangePassword">测试</el-button>
          </div>
        </template>
        
        <el-form :model="passwordTestForm" label-width="120px">
          <el-form-item label="原密码">
            <el-input v-model="passwordTestForm.oldPassword" type="password" />
          </el-form-item>
          <el-form-item label="新密码">
            <el-input v-model="passwordTestForm.newPassword" type="password" />
          </el-form-item>
          <el-form-item label="确认密码">
            <el-input v-model="passwordTestForm.confirmPassword" type="password" />
          </el-form-item>
        </el-form>
        
        <div v-if="passwordTestResult" class="test-result">
          <el-alert 
            :title="passwordTestResult.success ? '测试成功' : '测试失败'" 
            :type="passwordTestResult.success ? 'success' : 'error'"
            :description="passwordTestResult.message"
            show-icon
          />
        </div>
      </el-card>

      <!-- 更换邮箱测试 -->
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>更换邮箱测试</span>
            <el-button type="primary" @click="testUpdateEmail">测试</el-button>
          </div>
        </template>
        
        <el-form :model="emailTestForm" label-width="120px">
          <el-form-item label="新邮箱">
            <el-input v-model="emailTestForm.email" />
          </el-form-item>
        </el-form>
        
        <div v-if="emailTestResult" class="test-result">
          <el-alert 
            :title="emailTestResult.success ? '测试成功' : '测试失败'" 
            :type="emailTestResult.success ? 'success' : 'error'"
            :description="emailTestResult.message"
            show-icon
          />
        </div>
      </el-card>

      <!-- 更换手机号测试 -->
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>更换手机号测试</span>
            <el-button type="primary" @click="testUpdatePhone">测试</el-button>
          </div>
        </template>
        
        <el-form :model="phoneTestForm" label-width="120px">
          <el-form-item label="新手机号">
            <el-input v-model="phoneTestForm.phone" />
          </el-form-item>
        </el-form>
        
        <div v-if="phoneTestResult" class="test-result">
          <el-alert 
            :title="phoneTestResult.success ? '测试成功' : '测试失败'" 
            :type="phoneTestResult.success ? 'success' : 'error'"
            :description="phoneTestResult.message"
            show-icon
          />
        </div>
      </el-card>

      <!-- 更新用户信息测试 -->
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>更新用户信息测试</span>
            <el-button type="primary" @click="testUpdateUserInfo">测试</el-button>
          </div>
        </template>
        
        <el-form :model="userInfoTestForm" label-width="120px">
          <el-form-item label="真实姓名">
            <el-input v-model="userInfoTestForm.realName" />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="userInfoTestForm.phone" />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="userInfoTestForm.email" />
          </el-form-item>
        </el-form>
        
        <div v-if="userInfoTestResult" class="test-result">
          <el-alert 
            :title="userInfoTestResult.success ? '测试成功' : '测试失败'" 
            :type="userInfoTestResult.success ? 'success' : 'error'"
            :description="userInfoTestResult.message"
            show-icon
          />
        </div>
      </el-card>

      <!-- 当前用户信息显示 -->
      <el-card class="test-card">
        <template #header>
          <div class="card-header">
            <span>当前用户信息</span>
            <el-button type="primary" @click="refreshUserInfo">刷新</el-button>
          </div>
        </template>
        
        <div v-if="currentUserInfo">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="用户ID">{{ currentUserInfo.userId }}</el-descriptions-item>
            <el-descriptions-item label="用户名">{{ currentUserInfo.username }}</el-descriptions-item>
            <el-descriptions-item label="真实姓名">{{ currentUserInfo.realName }}</el-descriptions-item>
            <el-descriptions-item label="角色">{{ currentUserInfo.role }}</el-descriptions-item>
            <el-descriptions-item label="学校ID">{{ currentUserInfo.schoolId }}</el-descriptions-item>
            <el-descriptions-item label="学校名称">{{ currentUserInfo.schoolName }}</el-descriptions-item>
            <el-descriptions-item label="手机号">{{ currentUserInfo.phone || '未设置' }}</el-descriptions-item>
            <el-descriptions-item label="邮箱">{{ currentUserInfo.email || '未设置' }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 测试表单数据
const passwordTestForm = reactive({
  oldPassword: 'admin123',
  newPassword: 'newpass123',
  confirmPassword: 'newpass123'
})

const emailTestForm = reactive({
  email: '<EMAIL>'
})

const phoneTestForm = reactive({
  phone: '13900139001'
})

const userInfoTestForm = reactive({
  realName: '系统管理员(测试)',
  phone: '13800138000',
  email: '<EMAIL>'
})

// 测试结果
const passwordTestResult = ref(null)
const emailTestResult = ref(null)
const phoneTestResult = ref(null)
const userInfoTestResult = ref(null)

// 当前用户信息
const currentUserInfo = ref(null)

// 测试修改密码
const testChangePassword = async () => {
  try {
    await userStore.changePassword(
      passwordTestForm.oldPassword,
      passwordTestForm.newPassword,
      passwordTestForm.confirmPassword
    )
    
    passwordTestResult.value = {
      success: true,
      message: '密码修改成功'
    }
    
    // 恢复原密码
    setTimeout(async () => {
      try {
        await userStore.changePassword(
          passwordTestForm.newPassword,
          passwordTestForm.oldPassword,
          passwordTestForm.oldPassword
        )
        ElMessage.success('密码已恢复为原密码')
      } catch (error) {
        ElMessage.error('恢复原密码失败')
      }
    }, 1000)
    
  } catch (error) {
    passwordTestResult.value = {
      success: false,
      message: error.message || '密码修改失败'
    }
  }
}

// 测试更换邮箱
const testUpdateEmail = async () => {
  try {
    await userStore.updateEmail(emailTestForm.email)
    
    emailTestResult.value = {
      success: true,
      message: '邮箱更换成功'
    }
    
    // 刷新用户信息
    await refreshUserInfo()
    
  } catch (error) {
    emailTestResult.value = {
      success: false,
      message: error.message || '邮箱更换失败'
    }
  }
}

// 测试更换手机号
const testUpdatePhone = async () => {
  try {
    await userStore.bindPhone(phoneTestForm.phone)
    
    phoneTestResult.value = {
      success: true,
      message: '手机号更换成功'
    }
    
    // 刷新用户信息
    await refreshUserInfo()
    
  } catch (error) {
    phoneTestResult.value = {
      success: false,
      message: error.message || '手机号更换失败'
    }
  }
}

// 测试更新用户信息
const testUpdateUserInfo = async () => {
  try {
    await userStore.updateProfile(userInfoTestForm)
    
    userInfoTestResult.value = {
      success: true,
      message: '用户信息更新成功'
    }
    
    // 刷新用户信息
    await refreshUserInfo()
    
  } catch (error) {
    userInfoTestResult.value = {
      success: false,
      message: error.message || '用户信息更新失败'
    }
  }
}

// 刷新用户信息
const refreshUserInfo = async () => {
  try {
    const userInfo = await userStore.getUserInfo()
    currentUserInfo.value = userInfo
  } catch (error) {
    ElMessage.error('获取用户信息失败')
  }
}

// 页面加载时获取用户信息
onMounted(() => {
  refreshUserInfo()
})
</script>

<style scoped>
.profile-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h2 {
  color: #333;
  margin-bottom: 10px;
}

.header p {
  color: #666;
  font-size: 14px;
}

.test-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-result {
  margin-top: 15px;
}
</style>
