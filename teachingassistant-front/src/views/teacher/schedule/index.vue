<template>
  <div class="schedule-container">
    <div class="header">
      <h2>我的课表</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleExport">
          <el-icon><Download /></el-icon>
          导出课表
        </el-button>
      </div>
    </div>

    <div class="filter-bar">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="周次">
          <el-date-picker
            v-model="filterForm.week"
            type="week"
            placeholder="选择周次"
            format="YYYY年第WW周"
            value-format="YYYY-MM-DD"
            @change="handleWeekChange"
          />
        </el-form-item>
        <el-form-item label="班级">
          <el-select
            v-model="filterForm.classId"
            placeholder="请选择班级"
            clearable
            @change="fetchSchedule"
          >
            <el-option
              v-for="cls in classes"
              :key="cls.id"
              :label="cls.name"
              :value="cls.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <!-- 课表统计 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><Calendar /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ totalCourses }}</div>
                <div class="stats-label">本周总课时</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon completed">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ completedCourses }}</div>
                <div class="stats-label">已完成</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon upcoming">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ upcomingCourses }}</div>
                <div class="stats-label">待上课</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon classes">
                <el-icon><User /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ totalClasses }}</div>
                <div class="stats-label">授课班级</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="schedule-table-container">
      <el-table
        :data="scheduleData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="timeSlot" label="时间" width="120" fixed="left" />
        <el-table-column label="周一" width="200">
          <template #default="scope">
            <div
              v-for="course in scope.row.monday"
              :key="course.id"
              class="course-item"
              :class="getCourseClass(course)"
              @click="handleCourseClick(course)"
            >
              <div class="course-subject">{{ course.subject }}</div>
              <div class="course-class">{{ course.className }}</div>
              <div class="course-classroom">{{ course.classroom }}</div>
              <div class="course-status">{{ getCourseStatus(course) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="周二" width="200">
          <template #default="scope">
            <div
              v-for="course in scope.row.tuesday"
              :key="course.id"
              class="course-item"
              :class="getCourseClass(course)"
              @click="handleCourseClick(course)"
            >
              <div class="course-subject">{{ course.subject }}</div>
              <div class="course-class">{{ course.className }}</div>
              <div class="course-classroom">{{ course.classroom }}</div>
              <div class="course-status">{{ getCourseStatus(course) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="周三" width="200">
          <template #default="scope">
            <div
              v-for="course in scope.row.wednesday"
              :key="course.id"
              class="course-item"
              :class="getCourseClass(course)"
              @click="handleCourseClick(course)"
            >
              <div class="course-subject">{{ course.subject }}</div>
              <div class="course-class">{{ course.className }}</div>
              <div class="course-classroom">{{ course.classroom }}</div>
              <div class="course-status">{{ getCourseStatus(course) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="周四" width="200">
          <template #default="scope">
            <div
              v-for="course in scope.row.thursday"
              :key="course.id"
              class="course-item"
              :class="getCourseClass(course)"
              @click="handleCourseClick(course)"
            >
              <div class="course-subject">{{ course.subject }}</div>
              <div class="course-class">{{ course.className }}</div>
              <div class="course-classroom">{{ course.classroom }}</div>
              <div class="course-status">{{ getCourseStatus(course) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="周五" width="200">
          <template #default="scope">
            <div
              v-for="course in scope.row.friday"
              :key="course.id"
              class="course-item"
              :class="getCourseClass(course)"
              @click="handleCourseClick(course)"
            >
              <div class="course-subject">{{ course.subject }}</div>
              <div class="course-class">{{ course.className }}</div>
              <div class="course-classroom">{{ course.classroom }}</div>
              <div class="course-status">{{ getCourseStatus(course) }}</div>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 课程详情对话框 -->
    <el-dialog
      v-model="dialogVisible"
      title="课程详情"
      width="500px"
    >
      <div class="course-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="科目">{{ currentCourse.subject }}</el-descriptions-item>
          <el-descriptions-item label="班级">{{ currentCourse.className }}</el-descriptions-item>
          <el-descriptions-item label="教室">{{ currentCourse.classroom }}</el-descriptions-item>
          <el-descriptions-item label="时间">{{ currentCourse.timeSlot }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(currentCourse.status)">
              {{ getCourseStatus(currentCourse) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="学生人数">{{ currentCourse.studentCount }}人</el-descriptions-item>
        </el-descriptions>
        
        <div class="course-actions" v-if="currentCourse.status === 'upcoming'">
          <el-button type="success" @click="handleStartClass">开始上课</el-button>
          <el-button type="warning" @click="handleRequestLeave">请假</el-button>
        </div>
        
        <div class="course-actions" v-if="currentCourse.status === 'ongoing'">
          <el-button type="primary" @click="handleEndClass">结束课程</el-button>
          <el-button type="info" @click="handleTakeAttendance">点名</el-button>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Calendar, Check, Clock, User } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const scheduleData = ref([])
const classes = ref([])
const dialogVisible = ref(false)
const currentCourse = ref({})

// 统计数据
const totalCourses = ref(0)
const completedCourses = ref(0)
const upcomingCourses = ref(0)
const totalClasses = ref(0)

// 筛选表单
const filterForm = reactive({
  week: '',
  classId: ''
})

// 时间段配置
const timeSlots = [
  '08:00-08:45',
  '08:55-09:40',
  '10:00-10:45',
  '10:55-11:40',
  '14:00-14:45',
  '14:55-15:40',
  '16:00-16:45'
]

// 方法
const initScheduleData = () => {
  scheduleData.value = timeSlots.map(timeSlot => ({
    timeSlot,
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: []
  }))
}

const fetchClasses = async () => {
  try {
    // TODO: 调用API获取班级列表
    classes.value = [
      { id: 1, name: '一年级1班' },
      { id: 2, name: '一年级2班' },
      { id: 3, name: '二年级1班' }
    ]
  } catch (error) {
    ElMessage.error('获取班级列表失败')
  }
}

const fetchSchedule = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取课程表数据
    // 模拟数据
    const mockData = [
      {
        id: 1,
        subject: '数学',
        classId: 1,
        className: '一年级1班',
        classroom: '101',
        timeSlot: '08:00-08:45',
        dayOfWeek: 1,
        status: 'completed',
        studentCount: 35
      },
      {
        id: 2,
        subject: '数学',
        classId: 2,
        className: '一年级2班',
        classroom: '102',
        timeSlot: '08:55-09:40',
        dayOfWeek: 1,
        status: 'upcoming',
        studentCount: 32
      }
    ]
    
    // 重置课程表数据
    initScheduleData()
    
    // 填充课程数据
    mockData.forEach(course => {
      const timeSlotIndex = scheduleData.value.findIndex(slot => slot.timeSlot === course.timeSlot)
      if (timeSlotIndex !== -1) {
        const dayKey = ['', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'][course.dayOfWeek]
        if (dayKey) {
          scheduleData.value[timeSlotIndex][dayKey].push(course)
        }
      }
    })
    
    // 更新统计数据
    updateStats(mockData)
  } catch (error) {
    ElMessage.error('获取课程表失败')
  } finally {
    loading.value = false
  }
}

const updateStats = (courses) => {
  totalCourses.value = courses.length
  completedCourses.value = courses.filter(c => c.status === 'completed').length
  upcomingCourses.value = courses.filter(c => c.status === 'upcoming').length
  totalClasses.value = new Set(courses.map(c => c.classId)).size
}

const getCourseStatus = (course) => {
  const statusMap = {
    completed: '已完成',
    ongoing: '进行中',
    upcoming: '待上课',
    cancelled: '已取消'
  }
  return statusMap[course.status] || course.status
}

const getCourseClass = (course) => {
  return `course-${course.status}`
}

const getStatusTagType = (status) => {
  const typeMap = {
    completed: 'success',
    ongoing: 'primary',
    upcoming: 'warning',
    cancelled: 'danger'
  }
  return typeMap[status] || 'info'
}

const handleWeekChange = () => {
  fetchSchedule()
}

const handleExport = () => {
  ElMessage.info('导出课表功能待开发')
}

const handleCourseClick = (course) => {
  currentCourse.value = course
  dialogVisible.value = true
}

const handleStartClass = () => {
  ElMessage.success('开始上课')
  currentCourse.value.status = 'ongoing'
  dialogVisible.value = false
  fetchSchedule()
}

const handleEndClass = () => {
  ElMessage.success('课程结束')
  currentCourse.value.status = 'completed'
  dialogVisible.value = false
  fetchSchedule()
}

const handleTakeAttendance = () => {
  ElMessage.info('点名功能待开发')
}

const handleRequestLeave = () => {
  ElMessage.info('请假功能待开发')
}

// 生命周期
onMounted(() => {
  initScheduleData()
  fetchClasses()
  fetchSchedule()
})
</script>

<style scoped>
.schedule-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-bar {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-form {
  margin: 0;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.total {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.completed {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.upcoming {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.classes {
  background: linear-gradient(135deg, #909399, #a6a9ad);
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.schedule-table-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.course-item {
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 5px;
  cursor: pointer;
  transition: all 0.3s;
  border: 1px solid transparent;
}

.course-completed {
  background: #f0f9ff;
  border-color: #bfdbfe;
}

.course-completed:hover {
  background: #dbeafe;
  border-color: #93c5fd;
}

.course-ongoing {
  background: #eff6ff;
  border-color: #bfdbfe;
}

.course-ongoing:hover {
  background: #dbeafe;
  border-color: #60a5fa;
}

.course-upcoming {
  background: #fffbeb;
  border-color: #fed7aa;
}

.course-upcoming:hover {
  background: #fef3c7;
  border-color: #fbbf24;
}

.course-cancelled {
  background: #fef2f2;
  border-color: #fecaca;
}

.course-cancelled:hover {
  background: #fee2e2;
  border-color: #f87171;
}

.course-subject {
  font-weight: bold;
  color: #1e40af;
  margin-bottom: 2px;
}

.course-class {
  font-size: 12px;
  color: #6b7280;
}

.course-classroom {
  font-size: 12px;
  color: #6b7280;
}

.course-status {
  font-size: 11px;
  color: #9ca3af;
  margin-top: 2px;
}

.course-detail {
  padding: 20px 0;
}

.course-actions {
  margin-top: 20px;
  text-align: center;
}

.course-actions .el-button {
  margin: 0 5px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
