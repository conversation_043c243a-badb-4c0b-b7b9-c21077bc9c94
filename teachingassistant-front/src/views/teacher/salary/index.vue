<template>
  <div class="salary-container">
    <div class="header">
      <h2>工资查询</h2>
      <el-button type="primary" @click="handleExport">
        <el-icon><Download /></el-icon>
        导出工资单
      </el-button>
    </div>

    <!-- 工资统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon current">
                <el-icon><Money /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">¥{{ currentMonthSalary.toLocaleString() }}</div>
                <div class="stats-label">本月工资</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">¥{{ totalSalary.toLocaleString() }}</div>
                <div class="stats-label">累计收入</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon average">
                <el-icon><DataAnalysis /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">¥{{ averageSalary.toLocaleString() }}</div>
                <div class="stats-label">月均工资</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-value">{{ pendingCount }}</div>
                <div class="stats-label">待发放</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="年份">
          <el-select
            v-model="searchForm.year"
            placeholder="请选择年份"
            clearable
          >
            <el-option
              v-for="year in years"
              :key="year"
              :label="year + '年'"
              :value="year"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="月份">
          <el-select
            v-model="searchForm.month"
            placeholder="请选择月份"
            clearable
          >
            <el-option
              v-for="month in months"
              :key="month"
              :label="month + '月'"
              :value="month"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="已发放" value="paid" />
            <el-option label="待发放" value="pending" />
            <el-option label="已确认" value="confirmed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="year" label="年份" width="100" />
        <el-table-column prop="month" label="月份" width="100" />
        <el-table-column prop="baseSalary" label="基本工资" width="120">
          <template #default="scope">
            ¥{{ scope.row.baseSalary.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="performanceBonus" label="绩效奖金" width="120">
          <template #default="scope">
            ¥{{ scope.row.performanceBonus.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="classHourFee" label="课时费" width="120">
          <template #default="scope">
            ¥{{ scope.row.classHourFee.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="allowance" label="津贴补助" width="120">
          <template #default="scope">
            ¥{{ scope.row.allowance.toLocaleString() }}
          </template>
        </el-table-column>
        <el-table-column prop="deduction" label="扣除金额" width="120">
          <template #default="scope">
            <span class="deduction-amount">
              -¥{{ scope.row.deduction.toLocaleString() }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="totalSalary" label="实发工资" width="120">
          <template #default="scope">
            <span class="total-salary">
              ¥{{ scope.row.totalSalary.toLocaleString() }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="payDate" label="发放日期" width="120" />
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
            >
              查看详情
            </el-button>
            <el-button
              v-if="scope.row.status === 'pending'"
              type="success"
              size="small"
              @click="handleConfirm(scope.row)"
            >
              确认
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 工资详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="工资详情"
      width="600px"
    >
      <div class="salary-detail">
        <div class="detail-header">
          <h3>{{ currentSalary.year }}年{{ currentSalary.month }}月工资单</h3>
          <el-tag :type="getStatusTagType(currentSalary.status)">
            {{ getStatusText(currentSalary.status) }}
          </el-tag>
        </div>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="基本工资">
            ¥{{ currentSalary.baseSalary?.toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="绩效奖金">
            ¥{{ currentSalary.performanceBonus?.toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="课时费">
            ¥{{ currentSalary.classHourFee?.toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="津贴补助">
            ¥{{ currentSalary.allowance?.toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="应发工资">
            ¥{{ (currentSalary.baseSalary + currentSalary.performanceBonus + currentSalary.classHourFee + currentSalary.allowance)?.toLocaleString() }}
          </el-descriptions-item>
          <el-descriptions-item label="扣除金额">
            <span class="deduction-amount">
              -¥{{ currentSalary.deduction?.toLocaleString() }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="实发工资" :span="2">
            <span class="total-salary-large">
              ¥{{ currentSalary.totalSalary?.toLocaleString() }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="发放日期">
            {{ currentSalary.payDate || '待发放' }}
          </el-descriptions-item>
          <el-descriptions-item label="备注">
            {{ currentSalary.remark || '无' }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="salary-breakdown">
          <h4>工资构成明细</h4>
          <el-table
            :data="salaryBreakdown"
            border
            style="width: 100%"
            :show-summary="true"
            summary-text="合计"
          >
            <el-table-column prop="item" label="项目" />
            <el-table-column prop="amount" label="金额">
              <template #default="scope">
                <span :class="scope.row.type === 'deduction' ? 'deduction-amount' : ''">
                  {{ scope.row.type === 'deduction' ? '-' : '' }}¥{{ scope.row.amount.toLocaleString() }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="description" label="说明" />
          </el-table>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handlePrintSalary">打印工资单</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Money, TrendCharts, DataAnalysis, Clock } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const detailDialogVisible = ref(false)
const currentSalary = ref({})

// 统计数据
const currentMonthSalary = ref(0)
const totalSalary = ref(0)
const averageSalary = ref(0)
const pendingCount = ref(0)

// 搜索表单
const searchForm = reactive({
  year: '',
  month: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 年份和月份选项
const years = ref([])
const months = ref([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12])

// 工资明细
const salaryBreakdown = computed(() => {
  if (!currentSalary.value.id) return []
  
  return [
    { item: '基本工资', amount: currentSalary.value.baseSalary || 0, type: 'income', description: '固定基本工资' },
    { item: '绩效奖金', amount: currentSalary.value.performanceBonus || 0, type: 'income', description: '根据工作表现发放' },
    { item: '课时费', amount: currentSalary.value.classHourFee || 0, type: 'income', description: '按实际授课时数计算' },
    { item: '津贴补助', amount: currentSalary.value.allowance || 0, type: 'income', description: '各类津贴和补助' },
    { item: '个人所得税', amount: currentSalary.value.deduction || 0, type: 'deduction', description: '依法扣缴个人所得税' }
  ]
})

// 方法
const getStatusText = (status) => {
  const statusMap = {
    paid: '已发放',
    pending: '待发放',
    confirmed: '已确认'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const typeMap = {
    paid: 'success',
    pending: 'warning',
    confirmed: 'info'
  }
  return typeMap[status] || 'info'
}

const initYears = () => {
  const currentYear = new Date().getFullYear()
  years.value = []
  for (let i = currentYear; i >= currentYear - 5; i--) {
    years.value.push(i)
  }
}

const fetchStats = async () => {
  try {
    // TODO: 调用API获取统计数据
    // 模拟数据
    currentMonthSalary.value = 8500
    totalSalary.value = 85000
    averageSalary.value = 8500
    pendingCount.value = 1
  } catch (error) {
    ElMessage.error('获取统计数据失败')
  }
}

const fetchData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取工资记录
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        year: 2024,
        month: 1,
        baseSalary: 6000,
        performanceBonus: 1500,
        classHourFee: 800,
        allowance: 500,
        deduction: 300,
        totalSalary: 8500,
        status: 'paid',
        payDate: '2024-01-25',
        remark: '正常发放'
      },
      {
        id: 2,
        year: 2023,
        month: 12,
        baseSalary: 6000,
        performanceBonus: 1200,
        classHourFee: 750,
        allowance: 500,
        deduction: 280,
        totalSalary: 8170,
        status: 'paid',
        payDate: '2023-12-25',
        remark: '正常发放'
      }
    ]
    pagination.total = 2
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  searchForm.year = ''
  searchForm.month = ''
  searchForm.status = ''
  pagination.page = 1
  fetchData()
}

const handleExport = () => {
  ElMessage.info('导出工资单功能待开发')
}

const handleViewDetail = (row) => {
  currentSalary.value = row
  detailDialogVisible.value = true
}

const handleConfirm = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要确认${row.year}年${row.month}月的工资吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('工资确认成功')
    fetchData()
    fetchStats()
  } catch (error) {
    // 用户取消
  }
}

const handlePrintSalary = () => {
  ElMessage.info('打印工资单功能待开发')
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 生命周期
onMounted(() => {
  initYears()
  fetchStats()
  fetchData()
})
</script>

<style scoped>
.salary-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.current {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.total {
  background: linear-gradient(135deg, #409eff, #66b1ff);
}

.average {
  background: linear-gradient(135deg, #e6a23c, #ebb563);
}

.pending {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.stats-info {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.search-bar {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.deduction-amount {
  color: #f56c6c;
  font-weight: bold;
}

.total-salary {
  color: #67c23a;
  font-weight: bold;
  font-size: 16px;
}

.total-salary-large {
  color: #67c23a;
  font-weight: bold;
  font-size: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.salary-detail {
  padding: 20px 0;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.detail-header h3 {
  margin: 0;
  color: #303133;
}

.salary-breakdown {
  margin-top: 20px;
}

.salary-breakdown h4 {
  margin-bottom: 15px;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
