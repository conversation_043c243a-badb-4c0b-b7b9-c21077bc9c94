<template>
  <div class="attendance-container">
    <div class="header">
      <h2>考勤记录</h2>
      <el-button type="primary" @click="handleTakeAttendance">
        <el-icon><Clock /></el-icon>
        点名考勤
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="班级">
          <el-select
            v-model="searchForm.classId"
            placeholder="请选择班级"
            clearable
          >
            <el-option
              v-for="cls in classes"
              :key="cls.id"
              :label="cls.name"
              :value="cls.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="考勤状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="出席" value="present" />
            <el-option label="迟到" value="late" />
            <el-option label="早退" value="early_leave" />
            <el-option label="缺席" value="absent" />
            <el-option label="请假" value="leave" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="className" label="班级" width="120" />
        <el-table-column prop="subject" label="科目" width="100" />
        <el-table-column prop="timeSlot" label="时间段" width="150" />
        <el-table-column prop="totalStudents" label="应到人数" width="100" />
        <el-table-column prop="presentCount" label="实到人数" width="100" />
        <el-table-column prop="absentCount" label="缺席人数" width="100" />
        <el-table-column prop="attendanceRate" label="出勤率" width="100">
          <template #default="scope">
            <el-tag :type="getAttendanceRateType(scope.row.attendanceRate)">
              {{ scope.row.attendanceRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="记录时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
            >
              查看详情
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleEdit(scope.row)"
            >
              修改
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 点名考勤对话框 -->
    <el-dialog
      v-model="attendanceDialogVisible"
      title="点名考勤"
      width="800px"
      @close="handleAttendanceDialogClose"
    >
      <div class="attendance-form">
        <el-form :model="attendanceForm" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="班级">
                <el-select v-model="attendanceForm.classId" placeholder="请选择班级" style="width: 100%">
                  <el-option
                    v-for="cls in classes"
                    :key="cls.id"
                    :label="cls.name"
                    :value="cls.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="科目">
                <el-input v-model="attendanceForm.subject" placeholder="请输入科目" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="日期">
                <el-date-picker
                  v-model="attendanceForm.date"
                  type="date"
                  placeholder="选择日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="时间段">
                <el-input v-model="attendanceForm.timeSlot" placeholder="请输入时间段" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <div class="student-list">
          <h4>学生考勤</h4>
          <el-table
            :data="studentList"
            border
            style="width: 100%"
            max-height="400"
          >
            <el-table-column prop="id" label="学号" width="100" />
            <el-table-column prop="name" label="姓名" width="120" />
            <el-table-column label="考勤状态" width="200">
              <template #default="scope">
                <el-radio-group v-model="scope.row.status">
                  <el-radio label="present">出席</el-radio>
                  <el-radio label="late">迟到</el-radio>
                  <el-radio label="early_leave">早退</el-radio>
                  <el-radio label="absent">缺席</el-radio>
                  <el-radio label="leave">请假</el-radio>
                </el-radio-group>
              </template>
            </el-table-column>
            <el-table-column label="备注">
              <template #default="scope">
                <el-input
                  v-model="scope.row.remark"
                  placeholder="请输入备注"
                  size="small"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="attendanceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitAttendance">提交考勤</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 考勤详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="考勤详情"
      width="600px"
    >
      <div class="attendance-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="日期">{{ currentRecord.date }}</el-descriptions-item>
          <el-descriptions-item label="班级">{{ currentRecord.className }}</el-descriptions-item>
          <el-descriptions-item label="科目">{{ currentRecord.subject }}</el-descriptions-item>
          <el-descriptions-item label="时间段">{{ currentRecord.timeSlot }}</el-descriptions-item>
          <el-descriptions-item label="应到人数">{{ currentRecord.totalStudents }}人</el-descriptions-item>
          <el-descriptions-item label="实到人数">{{ currentRecord.presentCount }}人</el-descriptions-item>
          <el-descriptions-item label="缺席人数">{{ currentRecord.absentCount }}人</el-descriptions-item>
          <el-descriptions-item label="出勤率">{{ currentRecord.attendanceRate }}%</el-descriptions-item>
        </el-descriptions>

        <div class="student-detail-list">
          <h4>学生考勤详情</h4>
          <el-table
            :data="currentRecord.students || []"
            border
            style="width: 100%"
            max-height="300"
          >
            <el-table-column prop="name" label="姓名" />
            <el-table-column prop="status" label="状态">
              <template #default="scope">
                <el-tag :type="getStatusTagType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" />
          </el-table>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Clock } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const classes = ref([])
const studentList = ref([])
const attendanceDialogVisible = ref(false)
const detailDialogVisible = ref(false)
const currentRecord = ref({})

// 搜索表单
const searchForm = reactive({
  classId: '',
  dateRange: [],
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 考勤表单
const attendanceForm = reactive({
  classId: '',
  subject: '',
  date: '',
  timeSlot: ''
})

// 方法
const getAttendanceRateType = (rate) => {
  if (rate >= 95) return 'success'
  if (rate >= 85) return 'warning'
  return 'danger'
}

const getStatusText = (status) => {
  const statusMap = {
    present: '出席',
    late: '迟到',
    early_leave: '早退',
    absent: '缺席',
    leave: '请假'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status) => {
  const typeMap = {
    present: 'success',
    late: 'warning',
    early_leave: 'warning',
    absent: 'danger',
    leave: 'info'
  }
  return typeMap[status] || 'info'
}

const fetchClasses = async () => {
  try {
    // TODO: 调用API获取班级列表
    classes.value = [
      { id: 1, name: '一年级1班' },
      { id: 2, name: '一年级2班' },
      { id: 3, name: '二年级1班' }
    ]
  } catch (error) {
    ElMessage.error('获取班级列表失败')
  }
}

const fetchData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取考勤记录
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        date: '2024-01-15',
        className: '一年级1班',
        subject: '数学',
        timeSlot: '08:00-08:45',
        totalStudents: 35,
        presentCount: 33,
        absentCount: 2,
        attendanceRate: 94.3,
        createdAt: '2024-01-15 08:50:00',
        students: [
          { name: '张小明', status: 'present', remark: '' },
          { name: '李小红', status: 'late', remark: '迟到5分钟' },
          { name: '王小华', status: 'absent', remark: '生病请假' }
        ]
      }
    ]
    pagination.total = 1
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const fetchStudents = async (classId) => {
  try {
    // TODO: 调用API获取学生列表
    // 模拟数据
    studentList.value = [
      { id: 1001, name: '张小明', status: 'present', remark: '' },
      { id: 1002, name: '李小红', status: 'present', remark: '' },
      { id: 1003, name: '王小华', status: 'present', remark: '' }
    ]
  } catch (error) {
    ElMessage.error('获取学生列表失败')
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  searchForm.classId = ''
  searchForm.dateRange = []
  searchForm.status = ''
  pagination.page = 1
  fetchData()
}

const handleTakeAttendance = () => {
  resetAttendanceForm()
  attendanceDialogVisible.value = true
}

const handleViewDetail = (row) => {
  currentRecord.value = row
  detailDialogVisible.value = true
}

const handleEdit = (row) => {
  ElMessage.info('修改考勤功能待开发')
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条考勤记录吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmitAttendance = async () => {
  if (!attendanceForm.classId || !attendanceForm.subject || !attendanceForm.date || !attendanceForm.timeSlot) {
    ElMessage.warning('请填写完整的考勤信息')
    return
  }
  
  try {
    // TODO: 调用API提交考勤记录
    ElMessage.success('考勤记录提交成功')
    attendanceDialogVisible.value = false
    fetchData()
  } catch (error) {
    ElMessage.error('提交失败')
  }
}

const handleAttendanceDialogClose = () => {
  resetAttendanceForm()
}

const resetAttendanceForm = () => {
  attendanceForm.classId = ''
  attendanceForm.subject = ''
  attendanceForm.date = ''
  attendanceForm.timeSlot = ''
  studentList.value = []
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 监听班级选择变化
const watchClassChange = () => {
  if (attendanceForm.classId) {
    fetchStudents(attendanceForm.classId)
  }
}

// 生命周期
onMounted(() => {
  fetchClasses()
  fetchData()
})
</script>

<style scoped>
.attendance-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.search-bar {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.attendance-form {
  padding: 20px 0;
}

.student-list {
  margin-top: 20px;
}

.student-list h4 {
  margin-bottom: 15px;
  color: #303133;
}

.attendance-detail {
  padding: 20px 0;
}

.student-detail-list {
  margin-top: 20px;
}

.student-detail-list h4 {
  margin-bottom: 15px;
  color: #303133;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
