<template>
  <div class="students-container">
    <div class="header">
      <h2>我的学生</h2>
      <el-button type="primary" @click="handleExport">
        <el-icon><Download /></el-icon>
        导出学生名单
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="班级">
          <el-select
            v-model="searchForm.classId"
            placeholder="请选择班级"
            clearable
            @change="handleSearch"
          >
            <el-option
              v-for="cls in classes"
              :key="cls.id"
              :label="cls.name"
              :value="cls.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学生姓名">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入学生姓名"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="性别">
          <el-select
            v-model="searchForm.gender"
            placeholder="请选择性别"
            clearable
          >
            <el-option label="男" value="M" />
            <el-option label="女" value="F" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="学号" width="100" />
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="gender" label="性别" width="80">
          <template #default="scope">
            {{ scope.row.gender === 'M' ? '男' : '女' }}
          </template>
        </el-table-column>
        <el-table-column prop="age" label="年龄" width="80" />
        <el-table-column prop="className" label="班级" width="120" />
        <el-table-column prop="parentName" label="家长姓名" width="120" />
        <el-table-column prop="parentPhone" label="家长电话" width="130" />
        <el-table-column prop="averageScore" label="平均成绩" width="100">
          <template #default="scope">
            <el-tag :type="getScoreTagType(scope.row.averageScore)">
              {{ scope.row.averageScore }}分
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="attendanceRate" label="出勤率" width="100">
          <template #default="scope">
            <el-tag :type="getAttendanceTagType(scope.row.attendanceRate)">
              {{ scope.row.attendanceRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              type="success"
              size="small"
              @click="handleViewGrades(scope.row)"
            >
              成绩
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleViewAttendance(scope.row)"
            >
              考勤
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="handleContactParent(scope.row)"
            >
              联系家长
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 学生详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="学生详情"
      width="600px"
    >
      <div class="student-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="学号">{{ currentStudent.id }}</el-descriptions-item>
          <el-descriptions-item label="姓名">{{ currentStudent.name }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ currentStudent.gender === 'M' ? '男' : '女' }}</el-descriptions-item>
          <el-descriptions-item label="年龄">{{ currentStudent.age }}岁</el-descriptions-item>
          <el-descriptions-item label="班级">{{ currentStudent.className }}</el-descriptions-item>
          <el-descriptions-item label="入学时间">{{ currentStudent.enrollmentDate }}</el-descriptions-item>
          <el-descriptions-item label="家长姓名">{{ currentStudent.parentName }}</el-descriptions-item>
          <el-descriptions-item label="家长电话">{{ currentStudent.parentPhone }}</el-descriptions-item>
          <el-descriptions-item label="家庭地址" :span="2">{{ currentStudent.address }}</el-descriptions-item>
          <el-descriptions-item label="平均成绩">
            <el-tag :type="getScoreTagType(currentStudent.averageScore)">
              {{ currentStudent.averageScore }}分
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="出勤率">
            <el-tag :type="getAttendanceTagType(currentStudent.attendanceRate)">
              {{ currentStudent.attendanceRate }}%
            </el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <div class="student-notes">
          <h4>教师备注</h4>
          <el-input
            v-model="currentStudent.teacherNotes"
            type="textarea"
            :rows="4"
            placeholder="请输入对该学生的备注信息"
          />
          <div class="notes-actions">
            <el-button type="primary" size="small" @click="handleSaveNotes">保存备注</el-button>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 成绩查看对话框 -->
    <el-dialog
      v-model="gradesDialogVisible"
      title="学生成绩"
      width="700px"
    >
      <div class="grades-content">
        <div class="grades-header">
          <h4>{{ currentStudent.name }} - 成绩记录</h4>
        </div>
        <el-table
          :data="gradesList"
          border
          style="width: 100%"
        >
          <el-table-column prop="subject" label="科目" width="100" />
          <el-table-column prop="examType" label="考试类型" width="120" />
          <el-table-column prop="score" label="成绩" width="80">
            <template #default="scope">
              <el-tag :type="getScoreTagType(scope.row.score)">
                {{ scope.row.score }}分
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="fullScore" label="满分" width="80" />
          <el-table-column prop="rank" label="班级排名" width="100" />
          <el-table-column prop="examDate" label="考试日期" width="120" />
          <el-table-column prop="remark" label="备注" />
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="gradesDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 考勤查看对话框 -->
    <el-dialog
      v-model="attendanceDialogVisible"
      title="学生考勤"
      width="700px"
    >
      <div class="attendance-content">
        <div class="attendance-header">
          <h4>{{ currentStudent.name }} - 考勤记录</h4>
        </div>
        <el-table
          :data="attendanceList"
          border
          style="width: 100%"
        >
          <el-table-column prop="date" label="日期" width="120" />
          <el-table-column prop="subject" label="科目" width="100" />
          <el-table-column prop="timeSlot" label="时间段" width="150" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="getAttendanceStatusTagType(scope.row.status)">
                {{ getAttendanceStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" />
        </el-table>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="attendanceDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 联系家长对话框 -->
    <el-dialog
      v-model="contactDialogVisible"
      title="联系家长"
      width="500px"
    >
      <div class="contact-content">
        <el-form :model="contactForm" label-width="80px">
          <el-form-item label="家长姓名">
            <el-input v-model="contactForm.parentName" readonly />
          </el-form-item>
          <el-form-item label="联系电话">
            <el-input v-model="contactForm.parentPhone" readonly />
          </el-form-item>
          <el-form-item label="联系方式">
            <el-radio-group v-model="contactForm.method">
              <el-radio label="phone">电话</el-radio>
              <el-radio label="message">短信</el-radio>
              <el-radio label="wechat">微信</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="联系内容">
            <el-input
              v-model="contactForm.content"
              type="textarea"
              :rows="4"
              placeholder="请输入要沟通的内容"
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="contactDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSendContact">发送</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Download } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const classes = ref([])
const detailDialogVisible = ref(false)
const gradesDialogVisible = ref(false)
const attendanceDialogVisible = ref(false)
const contactDialogVisible = ref(false)
const currentStudent = ref({})
const gradesList = ref([])
const attendanceList = ref([])

// 搜索表单
const searchForm = reactive({
  classId: '',
  name: '',
  gender: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 联系家长表单
const contactForm = reactive({
  parentName: '',
  parentPhone: '',
  method: 'phone',
  content: ''
})

// 方法
const getScoreTagType = (score) => {
  if (score >= 90) return 'success'
  if (score >= 80) return 'warning'
  if (score >= 60) return 'info'
  return 'danger'
}

const getAttendanceTagType = (rate) => {
  if (rate >= 95) return 'success'
  if (rate >= 85) return 'warning'
  return 'danger'
}

const getAttendanceStatusText = (status) => {
  const statusMap = {
    present: '出席',
    late: '迟到',
    early_leave: '早退',
    absent: '缺席',
    leave: '请假'
  }
  return statusMap[status] || status
}

const getAttendanceStatusTagType = (status) => {
  const typeMap = {
    present: 'success',
    late: 'warning',
    early_leave: 'warning',
    absent: 'danger',
    leave: 'info'
  }
  return typeMap[status] || 'info'
}

const fetchClasses = async () => {
  try {
    // TODO: 调用API获取班级列表
    classes.value = [
      { id: 1, name: '一年级1班' },
      { id: 2, name: '一年级2班' },
      { id: 3, name: '二年级1班' }
    ]
  } catch (error) {
    ElMessage.error('获取班级列表失败')
  }
}

const fetchData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取学生列表
    // 模拟数据
    tableData.value = [
      {
        id: 1001,
        name: '张小明',
        gender: 'M',
        age: 7,
        className: '一年级1班',
        parentName: '张父',
        parentPhone: '13800138001',
        averageScore: 85,
        attendanceRate: 96,
        enrollmentDate: '2023-09-01',
        address: '北京市朝阳区示例街道123号',
        teacherNotes: '学习认真，但需要加强数学练习'
      },
      {
        id: 1002,
        name: '李小红',
        gender: 'F',
        age: 7,
        className: '一年级1班',
        parentName: '李母',
        parentPhone: '13800138002',
        averageScore: 92,
        attendanceRate: 98,
        enrollmentDate: '2023-09-01',
        address: '北京市朝阳区示例街道456号',
        teacherNotes: '优秀学生，各方面表现都很好'
      }
    ]
    pagination.total = 2
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const fetchGrades = async (studentId) => {
  try {
    // TODO: 调用API获取学生成绩
    // 模拟数据
    gradesList.value = [
      {
        subject: '数学',
        examType: '期中考试',
        score: 85,
        fullScore: 100,
        rank: 15,
        examDate: '2024-01-10',
        remark: '计算题有待提高'
      },
      {
        subject: '语文',
        examType: '期中考试',
        score: 88,
        fullScore: 100,
        rank: 12,
        examDate: '2024-01-08',
        remark: '作文写得很好'
      }
    ]
  } catch (error) {
    ElMessage.error('获取成绩数据失败')
  }
}

const fetchAttendance = async (studentId) => {
  try {
    // TODO: 调用API获取学生考勤
    // 模拟数据
    attendanceList.value = [
      {
        date: '2024-01-15',
        subject: '数学',
        timeSlot: '08:00-08:45',
        status: 'present',
        remark: ''
      },
      {
        date: '2024-01-14',
        subject: '语文',
        timeSlot: '08:55-09:40',
        status: 'late',
        remark: '迟到5分钟'
      }
    ]
  } catch (error) {
    ElMessage.error('获取考勤数据失败')
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  searchForm.classId = ''
  searchForm.name = ''
  searchForm.gender = ''
  pagination.page = 1
  fetchData()
}

const handleExport = () => {
  ElMessage.info('导出学生名单功能待开发')
}

const handleViewDetail = (row) => {
  currentStudent.value = { ...row }
  detailDialogVisible.value = true
}

const handleViewGrades = (row) => {
  currentStudent.value = row
  fetchGrades(row.id)
  gradesDialogVisible.value = true
}

const handleViewAttendance = (row) => {
  currentStudent.value = row
  fetchAttendance(row.id)
  attendanceDialogVisible.value = true
}

const handleContactParent = (row) => {
  currentStudent.value = row
  contactForm.parentName = row.parentName
  contactForm.parentPhone = row.parentPhone
  contactForm.method = 'phone'
  contactForm.content = ''
  contactDialogVisible.value = true
}

const handleSaveNotes = async () => {
  try {
    // TODO: 调用API保存教师备注
    ElMessage.success('备注保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const handleSendContact = async () => {
  if (!contactForm.content.trim()) {
    ElMessage.warning('请输入联系内容')
    return
  }
  
  try {
    // TODO: 调用API发送联系信息
    ElMessage.success('联系信息发送成功')
    contactDialogVisible.value = false
  } catch (error) {
    ElMessage.error('发送失败')
  }
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 生命周期
onMounted(() => {
  fetchClasses()
  fetchData()
})
</script>

<style scoped>
.students-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.search-bar {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.student-detail {
  padding: 20px 0;
}

.student-notes {
  margin-top: 20px;
}

.student-notes h4 {
  margin-bottom: 15px;
  color: #303133;
}

.notes-actions {
  margin-top: 10px;
  text-align: right;
}

.grades-content,
.attendance-content {
  padding: 20px 0;
}

.grades-header,
.attendance-header {
  margin-bottom: 15px;
}

.grades-header h4,
.attendance-header h4 {
  margin: 0;
  color: #303133;
}

.contact-content {
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
