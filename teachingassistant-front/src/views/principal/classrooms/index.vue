<template>
  <div class="classrooms-container">
    <div class="header">
      <h2>教室管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加教室
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="教室名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入教室名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="楼层">
          <el-select
            v-model="searchForm.floor"
            placeholder="请选择楼层"
            clearable
          >
            <el-option
              v-for="option in floorOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择教室类型"
            clearable
          >
            <el-option
              v-for="option in typeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
      >
        <el-table-column prop="classroomId" label="ID" width="80" />
        <el-table-column prop="name" label="教室名称" />
        <el-table-column prop="floor" label="楼层" width="100">
          <template #default="scope">
            {{ scope.row.floor ? scope.row.floor + '楼' : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="capacity" label="容量" width="100">
          <template #default="scope">
            {{ scope.row.capacity }}人
          </template>
        </el-table-column>
        <el-table-column prop="equipment" label="设备" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getStatusTagType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="handleViewSchedule(scope.row)"
            >
              使用情况
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="教室名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入教室名称" />
        </el-form-item>
        <el-form-item label="楼层" prop="floor">
          <el-select v-model="form.floor" placeholder="请选择楼层" style="width: 100%">
            <el-option
              v-for="option in floorOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择教室类型" style="width: 100%">
            <el-option
              v-for="option in typeOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="容量" prop="capacity">
          <el-input-number
            v-model="form.capacity"
            :min="1"
            :max="500"
            placeholder="请输入教室容量"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="设备" prop="equipment">
          <el-input
            v-model="form.equipment"
            type="textarea"
            :rows="3"
            placeholder="请输入教室设备信息"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.value"
            >
              {{ option.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import {
  principalClassroomApi,
  classroomTypeOptions,
  classroomStatusOptions,
  getClassroomTypeText,
  getClassroomStatusText,
  getClassroomTypeTagType,
  getClassroomStatusTagType
} from '@/api/classroom'
import type { Classroom } from '@/types'

// 响应式数据
const loading = ref(false)
const tableData = ref<Classroom[]>([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  floor: '',
  type: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表单数据
const form = reactive({
  classroomId: null,
  name: '',
  floor: null,
  type: 'normal',
  capacity: 30,
  equipment: '',
  status: 'available'
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入教室名称', trigger: 'blur' },
    { max: 50, message: '教室名称长度不能超过50个字符', trigger: 'blur' }
  ],
  floor: [
    { type: 'number', min: 1, max: 50, message: '楼层必须在1-50之间', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择教室类型', trigger: 'change' }
  ],
  capacity: [
    { required: true, message: '请输入容量', trigger: 'blur' },
    { type: 'number', min: 1, max: 500, message: '容量必须在1-500之间', trigger: 'blur' }
  ],
  equipment: [
    { max: 500, message: '设备描述长度不能超过500个字符', trigger: 'blur' }
  ]
}

// 选项数据
const typeOptions = classroomTypeOptions
const statusOptions = classroomStatusOptions

// 楼层选项
const floorOptions = Array.from({ length: 20 }, (_, i) => ({
  label: `${i + 1}楼`,
  value: i + 1
}))

// 工具方法
const getTypeText = getClassroomTypeText
const getTypeTagType = getClassroomTypeTagType
const getStatusText = getClassroomStatusText
const getStatusTagType = getClassroomStatusTagType

const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      name: searchForm.name || undefined,
      floor: searchForm.floor || undefined,
      type: searchForm.type || undefined,
      status: searchForm.status || undefined
    }

    const response = await principalClassroomApi.getList(params)
    tableData.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('获取教室列表失败:', error)
    ElMessage.error('获取教室列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  searchForm.name = ''
  searchForm.floor = ''
  searchForm.type = ''
  pagination.page = 1
  fetchData()
}

const handleAdd = () => {
  dialogTitle.value = '添加教室'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑教室'
  Object.assign(form, {
    classroomId: row.classroomId,
    name: row.name,
    floor: row.floor,
    type: row.type,
    capacity: row.capacity,
    equipment: row.equipment,
    status: row.status
  })
  dialogVisible.value = true
}

const handleViewSchedule = (row) => {
  ElMessage.info(`查看${row.name}的使用情况功能待开发`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除教室"${row.name}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await principalClassroomApi.delete(row.classroomId)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除教室失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    const data = {
      name: form.name,
      floor: form.floor,
      type: form.type,
      capacity: form.capacity,
      equipment: form.equipment,
      status: form.status
    }

    if (form.classroomId) {
      // 编辑
      await principalClassroomApi.update(form.classroomId, data)
      ElMessage.success('更新成功')
    } else {
      // 新增
      await principalClassroomApi.create(data)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    fetchData()
  } catch (error) {
    console.error('保存教室失败:', error)
    ElMessage.error('保存失败')
  }
}

const handleDialogClose = () => {
  formRef.value?.resetFields()
}

const resetForm = () => {
  form.classroomId = null
  form.name = ''
  form.floor = null
  form.type = 'normal'
  form.capacity = 30
  form.equipment = ''
  form.status = 'available'

  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.classrooms-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.search-bar {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
