<template>
  <div class="classrooms-container">
    <div class="header">
      <h2>教室管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加教室
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="教室名称">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入教室名称"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="楼层">
          <el-select
            v-model="searchForm.floor"
            placeholder="请选择楼层"
            clearable
          >
            <el-option label="1楼" value="1" />
            <el-option label="2楼" value="2" />
            <el-option label="3楼" value="3" />
            <el-option label="4楼" value="4" />
            <el-option label="5楼" value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select
            v-model="searchForm.type"
            placeholder="请选择教室类型"
            clearable
          >
            <el-option label="普通教室" value="normal" />
            <el-option label="多媒体教室" value="multimedia" />
            <el-option label="实验室" value="lab" />
            <el-option label="音乐教室" value="music" />
            <el-option label="美术教室" value="art" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="name" label="教室名称" />
        <el-table-column prop="floor" label="楼层" width="100">
          <template #default="scope">
            {{ scope.row.floor }}楼
          </template>
        </el-table-column>
        <el-table-column prop="type" label="类型" width="120">
          <template #default="scope">
            <el-tag :type="getTypeTagType(scope.row.type)">
              {{ getTypeText(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="capacity" label="容量" width="100">
          <template #default="scope">
            {{ scope.row.capacity }}人
          </template>
        </el-table-column>
        <el-table-column prop="equipment" label="设备" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '可用' : '维修中' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="handleViewSchedule(scope.row)"
            >
              使用情况
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="教室名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入教室名称" />
        </el-form-item>
        <el-form-item label="楼层" prop="floor">
          <el-select v-model="form.floor" placeholder="请选择楼层" style="width: 100%">
            <el-option label="1楼" value="1" />
            <el-option label="2楼" value="2" />
            <el-option label="3楼" value="3" />
            <el-option label="4楼" value="4" />
            <el-option label="5楼" value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择教室类型" style="width: 100%">
            <el-option label="普通教室" value="normal" />
            <el-option label="多媒体教室" value="multimedia" />
            <el-option label="实验室" value="lab" />
            <el-option label="音乐教室" value="music" />
            <el-option label="美术教室" value="art" />
          </el-select>
        </el-form-item>
        <el-form-item label="容量" prop="capacity">
          <el-input-number
            v-model="form.capacity"
            :min="1"
            :max="100"
            placeholder="请输入教室容量"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="设备" prop="equipment">
          <el-input
            v-model="form.equipment"
            type="textarea"
            :rows="3"
            placeholder="请输入教室设备信息"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">可用</el-radio>
            <el-radio :label="0">维修中</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()

// 搜索表单
const searchForm = reactive({
  name: '',
  floor: '',
  type: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表单数据
const form = reactive({
  id: null,
  name: '',
  floor: '',
  type: '',
  capacity: 30,
  equipment: '',
  status: 1
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入教室名称', trigger: 'blur' }
  ],
  floor: [
    { required: true, message: '请选择楼层', trigger: 'change' }
  ],
  type: [
    { required: true, message: '请选择教室类型', trigger: 'change' }
  ],
  capacity: [
    { required: true, message: '请输入教室容量', trigger: 'blur' }
  ]
}

// 方法
const getTypeText = (type) => {
  const typeMap = {
    normal: '普通教室',
    multimedia: '多媒体教室',
    lab: '实验室',
    music: '音乐教室',
    art: '美术教室'
  }
  return typeMap[type] || type
}

const getTypeTagType = (type) => {
  const typeMap = {
    normal: 'info',
    multimedia: 'success',
    lab: 'warning',
    music: 'danger',
    art: 'primary'
  }
  return typeMap[type] || 'info'
}

const fetchData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取教室列表
    // 模拟数据
    tableData.value = [
      {
        id: 1,
        name: '101教室',
        floor: '1',
        type: 'normal',
        capacity: 40,
        equipment: '黑板、投影仪、音响',
        status: 1,
        createdAt: '2024-01-01 10:00:00'
      },
      {
        id: 2,
        name: '多媒体教室A',
        floor: '2',
        type: 'multimedia',
        capacity: 50,
        equipment: '电子白板、投影仪、音响、电脑',
        status: 1,
        createdAt: '2024-01-01 10:00:00'
      }
    ]
    pagination.total = 2
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  searchForm.name = ''
  searchForm.floor = ''
  searchForm.type = ''
  pagination.page = 1
  fetchData()
}

const handleAdd = () => {
  dialogTitle.value = '添加教室'
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row) => {
  dialogTitle.value = '编辑教室'
  Object.assign(form, row)
  dialogVisible.value = true
}

const handleViewSchedule = (row) => {
  ElMessage.info(`查看${row.name}的使用情况功能待开发`)
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除教室"${row.name}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    ElMessage.success('保存成功')
    dialogVisible.value = false
    fetchData()
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const handleDialogClose = () => {
  formRef.value?.resetFields()
}

const resetForm = () => {
  form.id = null
  form.name = ''
  form.floor = ''
  form.type = ''
  form.capacity = 30
  form.equipment = ''
  form.status = 1
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.classrooms-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.search-bar {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
