<template>
  <div class="messages-container">
    <div class="header">
      <h2>消息中心</h2>
      <el-button type="primary" @click="handleSend">
        <el-icon><Message /></el-icon>
        发送消息
      </el-button>
    </div>

    <div class="message-tabs">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="收件箱" name="inbox">
          <div class="search-bar">
            <el-form :inline="true" :model="searchForm" class="search-form">
              <el-form-item label="发送人">
                <el-input
                  v-model="searchForm.sender"
                  placeholder="请输入发送人"
                  clearable
                  @keyup.enter="handleSearch"
                />
              </el-form-item>
              <el-form-item label="状态">
                <el-select
                  v-model="searchForm.status"
                  placeholder="请选择状态"
                  clearable
                >
                  <el-option label="未读" value="unread" />
                  <el-option label="已读" value="read" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch">搜索</el-button>
                <el-button @click="handleReset">重置</el-button>
              </el-form-item>
            </el-form>
          </div>

          <div class="table-container">
            <el-table
              :data="inboxData"
              v-loading="loading"
              border
              style="width: 100%"
              @row-click="handleRowClick"
            >
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="sender" label="发送人" width="120" />
              <el-table-column prop="title" label="标题" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'unread' ? 'danger' : 'success'">
                    {{ scope.row.status === 'unread' ? '未读' : '已读' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createdAt" label="接收时间" width="180" />
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="scope">
                  <el-button
                    type="primary"
                    size="small"
                    @click.stop="handleView(scope.row)"
                  >
                    查看
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click.stop="handleDelete(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="发件箱" name="outbox">
          <div class="table-container">
            <el-table
              :data="outboxData"
              v-loading="loading"
              border
              style="width: 100%"
            >
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="receiver" label="接收人" width="120" />
              <el-table-column prop="title" label="标题" />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="scope">
                  <el-tag :type="scope.row.status === 'sent' ? 'success' : 'warning'">
                    {{ scope.row.status === 'sent' ? '已发送' : '草稿' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createdAt" label="发送时间" width="180" />
              <el-table-column label="操作" width="150" fixed="right">
                <template #default="scope">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handleView(scope.row)"
                  >
                    查看
                  </el-button>
                  <el-button
                    type="danger"
                    size="small"
                    @click="handleDelete(scope.row)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="pagination">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 发送消息对话框 -->
    <el-dialog
      v-model="sendDialogVisible"
      title="发送消息"
      width="600px"
      @close="handleSendDialogClose"
    >
      <el-form
        ref="sendFormRef"
        :model="sendForm"
        :rules="sendRules"
        label-width="80px"
      >
        <el-form-item label="接收人" prop="receiverType">
          <el-radio-group v-model="sendForm.receiverType" @change="handleReceiverTypeChange">
            <el-radio label="teacher">老师</el-radio>
            <el-radio label="student">学生</el-radio>
            <el-radio label="all">全体</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="sendForm.receiverType !== 'all'" label="选择接收人" prop="receivers">
          <el-select
            v-model="sendForm.receivers"
            multiple
            placeholder="请选择接收人"
            style="width: 100%"
          >
            <el-option
              v-for="user in availableUsers"
              :key="user.id"
              :label="user.name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="sendForm.title" placeholder="请输入消息标题" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="sendForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入消息内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="sendDialogVisible = false">取消</el-button>
          <el-button @click="handleSaveDraft">保存草稿</el-button>
          <el-button type="primary" @click="handleSendMessage">发送</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看消息对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="消息详情"
      width="600px"
    >
      <div class="message-detail">
        <div class="message-header">
          <h3>{{ currentMessage.title }}</h3>
          <div class="message-meta">
            <span>发送人：{{ currentMessage.sender }}</span>
            <span>时间：{{ currentMessage.createdAt }}</span>
          </div>
        </div>
        <div class="message-content">
          {{ currentMessage.content }}
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="viewDialogVisible = false">关闭</el-button>
          <el-button v-if="activeTab === 'inbox'" type="primary" @click="handleReply">回复</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Message } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const activeTab = ref('inbox')
const inboxData = ref([])
const outboxData = ref([])
const sendDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const sendFormRef = ref()
const currentMessage = ref({})

// 搜索表单
const searchForm = reactive({
  sender: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 发送消息表单
const sendForm = reactive({
  receiverType: 'teacher',
  receivers: [],
  title: '',
  content: ''
})

// 发送消息验证规则
const sendRules = {
  receiverType: [
    { required: true, message: '请选择接收人类型', trigger: 'change' }
  ],
  receivers: [
    { required: true, message: '请选择接收人', trigger: 'change' }
  ],
  title: [
    { required: true, message: '请输入消息标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入消息内容', trigger: 'blur' }
  ]
}

// 可选用户列表
const teachers = ref([])
const students = ref([])

// 计算属性
const availableUsers = computed(() => {
  if (sendForm.receiverType === 'teacher') {
    return teachers.value
  } else if (sendForm.receiverType === 'student') {
    return students.value
  }
  return []
})

// 方法
const fetchUsers = async () => {
  try {
    // TODO: 调用API获取用户列表
    teachers.value = [
      { id: 1, name: '张老师' },
      { id: 2, name: '李老师' },
      { id: 3, name: '王老师' }
    ]
    
    students.value = [
      { id: 1, name: '张小明' },
      { id: 2, name: '李小红' },
      { id: 3, name: '王小华' }
    ]
  } catch (error) {
    ElMessage.error('获取用户列表失败')
  }
}

const fetchInboxData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取收件箱数据
    // 模拟数据
    inboxData.value = [
      {
        id: 1,
        sender: '张老师',
        title: '关于下周课程安排的通知',
        content: '下周一的数学课调整到周二进行，请注意安排。',
        status: 'unread',
        createdAt: '2024-01-10 10:00:00'
      }
    ]
    pagination.total = 1
  } catch (error) {
    ElMessage.error('获取收件箱数据失败')
  } finally {
    loading.value = false
  }
}

const fetchOutboxData = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取发件箱数据
    // 模拟数据
    outboxData.value = [
      {
        id: 1,
        receiver: '全体老师',
        title: '学期末工作安排',
        content: '请各位老师做好学期末的相关工作安排。',
        status: 'sent',
        createdAt: '2024-01-09 15:00:00'
      }
    ]
    pagination.total = 1
  } catch (error) {
    ElMessage.error('获取发件箱数据失败')
  } finally {
    loading.value = false
  }
}

const handleTabChange = (tab) => {
  if (tab === 'inbox') {
    fetchInboxData()
  } else {
    fetchOutboxData()
  }
}

const handleSearch = () => {
  pagination.page = 1
  if (activeTab.value === 'inbox') {
    fetchInboxData()
  } else {
    fetchOutboxData()
  }
}

const handleReset = () => {
  searchForm.sender = ''
  searchForm.status = ''
  pagination.page = 1
  if (activeTab.value === 'inbox') {
    fetchInboxData()
  } else {
    fetchOutboxData()
  }
}

const handleRowClick = (row) => {
  handleView(row)
}

const handleView = (row) => {
  currentMessage.value = row
  viewDialogVisible.value = true
  
  // 如果是未读消息，标记为已读
  if (row.status === 'unread') {
    row.status = 'read'
    // TODO: 调用API标记为已读
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除消息"${row.title}"吗？`,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('删除成功')
    if (activeTab.value === 'inbox') {
      fetchInboxData()
    } else {
      fetchOutboxData()
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const handleSend = () => {
  resetSendForm()
  sendDialogVisible.value = true
}

const handleReceiverTypeChange = () => {
  sendForm.receivers = []
}

const handleSaveDraft = async () => {
  try {
    await sendFormRef.value.validate()
    ElMessage.success('草稿保存成功')
    sendDialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存草稿失败')
  }
}

const handleSendMessage = async () => {
  try {
    await sendFormRef.value.validate()
    ElMessage.success('消息发送成功')
    sendDialogVisible.value = false
    fetchOutboxData()
  } catch (error) {
    ElMessage.error('消息发送失败')
  }
}

const handleReply = () => {
  sendForm.receiverType = 'teacher'
  sendForm.receivers = [1] // 假设回复给发送人
  sendForm.title = `回复：${currentMessage.value.title}`
  sendForm.content = ''
  viewDialogVisible.value = false
  sendDialogVisible.value = true
}

const handleSendDialogClose = () => {
  sendFormRef.value?.resetFields()
}

const resetSendForm = () => {
  sendForm.receiverType = 'teacher'
  sendForm.receivers = []
  sendForm.title = ''
  sendForm.content = ''
}

const handleSizeChange = (size) => {
  pagination.size = size
  if (activeTab.value === 'inbox') {
    fetchInboxData()
  } else {
    fetchOutboxData()
  }
}

const handleCurrentChange = (page) => {
  pagination.page = page
  if (activeTab.value === 'inbox') {
    fetchInboxData()
  } else {
    fetchOutboxData()
  }
}

// 生命周期
onMounted(() => {
  fetchUsers()
  fetchInboxData()
})
</script>

<style scoped>
.messages-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.message-tabs {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-bar {
  padding: 20px;
  border-bottom: 1px solid #ebeef5;
}

.search-form {
  margin: 0;
}

.table-container {
  padding: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.message-detail {
  padding: 20px;
}

.message-header {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.message-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.message-meta {
  display: flex;
  gap: 20px;
  color: #909399;
  font-size: 14px;
}

.message-content {
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
