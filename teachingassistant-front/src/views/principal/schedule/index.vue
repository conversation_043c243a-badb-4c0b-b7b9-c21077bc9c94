<template>
  <div class="schedule-container">
    <div class="header">
      <h2>排课管理</h2>
      <div class="header-actions">
        <el-button type="success" @click="handleAutoSchedule">
          <el-icon><MagicStick /></el-icon>
          智能排课
        </el-button>
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          手动排课
        </el-button>
      </div>
    </div>

    <div class="filter-bar">
      <el-form :inline="true" :model="filterForm" class="filter-form">
        <el-form-item label="周次">
          <el-date-picker
            v-model="filterForm.week"
            type="week"
            placeholder="选择周次"
            format="YYYY年第WW周"
            value-format="YYYY-MM-DD"
            @change="handleWeekChange"
          />
        </el-form-item>
        <el-form-item label="班级">
          <el-select
            v-model="filterForm.classId"
            placeholder="请选择班级"
            clearable
            @change="fetchSchedule"
          >
            <el-option
              v-for="cls in classes"
              :key="cls.id"
              :label="cls.name"
              :value="cls.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="老师">
          <el-select
            v-model="filterForm.teacherId"
            placeholder="请选择老师"
            clearable
            @change="fetchSchedule"
          >
            <el-option
              v-for="teacher in teachers"
              :key="teacher.id"
              :label="teacher.name"
              :value="teacher.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <div class="schedule-table-container">
      <el-table
        :data="scheduleData"
        border
        style="width: 100%"
        v-loading="loading"
      >
        <el-table-column prop="timeSlot" label="时间" width="120" fixed="left" />
        <el-table-column label="周一" width="200">
          <template #default="scope">
            <div
              v-for="course in scope.row.monday"
              :key="course.id"
              class="course-item"
              @click="handleEditCourse(course)"
            >
              <div class="course-subject">{{ course.subject }}</div>
              <div class="course-teacher">{{ course.teacherName }}</div>
              <div class="course-class">{{ course.className }}</div>
            </div>
            <el-button
              v-if="!scope.row.monday.length"
              type="text"
              @click="handleAddCourse(scope.row.timeSlot, 1)"
            >
              + 添加课程
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="周二" width="200">
          <template #default="scope">
            <div
              v-for="course in scope.row.tuesday"
              :key="course.id"
              class="course-item"
              @click="handleEditCourse(course)"
            >
              <div class="course-subject">{{ course.subject }}</div>
              <div class="course-teacher">{{ course.teacherName }}</div>
              <div class="course-class">{{ course.className }}</div>
            </div>
            <el-button
              v-if="!scope.row.tuesday.length"
              type="text"
              @click="handleAddCourse(scope.row.timeSlot, 2)"
            >
              + 添加课程
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="周三" width="200">
          <template #default="scope">
            <div
              v-for="course in scope.row.wednesday"
              :key="course.id"
              class="course-item"
              @click="handleEditCourse(course)"
            >
              <div class="course-subject">{{ course.subject }}</div>
              <div class="course-teacher">{{ course.teacherName }}</div>
              <div class="course-class">{{ course.className }}</div>
            </div>
            <el-button
              v-if="!scope.row.wednesday.length"
              type="text"
              @click="handleAddCourse(scope.row.timeSlot, 3)"
            >
              + 添加课程
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="周四" width="200">
          <template #default="scope">
            <div
              v-for="course in scope.row.thursday"
              :key="course.id"
              class="course-item"
              @click="handleEditCourse(course)"
            >
              <div class="course-subject">{{ course.subject }}</div>
              <div class="course-teacher">{{ course.teacherName }}</div>
              <div class="course-class">{{ course.className }}</div>
            </div>
            <el-button
              v-if="!scope.row.thursday.length"
              type="text"
              @click="handleAddCourse(scope.row.timeSlot, 4)"
            >
              + 添加课程
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="周五" width="200">
          <template #default="scope">
            <div
              v-for="course in scope.row.friday"
              :key="course.id"
              class="course-item"
              @click="handleEditCourse(course)"
            >
              <div class="course-subject">{{ course.subject }}</div>
              <div class="course-teacher">{{ course.teacherName }}</div>
              <div class="course-class">{{ course.className }}</div>
            </div>
            <el-button
              v-if="!scope.row.friday.length"
              type="text"
              @click="handleAddCourse(scope.row.timeSlot, 5)"
            >
              + 添加课程
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加/编辑课程对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="500px"
      @close="handleDialogClose"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="科目" prop="subject">
          <el-input v-model="form.subject" placeholder="请输入科目" />
        </el-form-item>
        <el-form-item label="老师" prop="teacherId">
          <el-select v-model="form.teacherId" placeholder="请选择老师" style="width: 100%">
            <el-option
              v-for="teacher in teachers"
              :key="teacher.id"
              :label="teacher.name"
              :value="teacher.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="班级" prop="classId">
          <el-select v-model="form.classId" placeholder="请选择班级" style="width: 100%">
            <el-option
              v-for="cls in classes"
              :key="cls.id"
              :label="cls.name"
              :value="cls.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="教室" prop="classroom">
          <el-input v-model="form.classroom" placeholder="请输入教室" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, MagicStick } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const scheduleData = ref([])
const classes = ref([])
const teachers = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()

// 筛选表单
const filterForm = reactive({
  week: '',
  classId: '',
  teacherId: ''
})

// 表单数据
const form = reactive({
  id: null,
  subject: '',
  teacherId: '',
  classId: '',
  classroom: '',
  timeSlot: '',
  dayOfWeek: 1
})

// 表单验证规则
const rules = {
  subject: [
    { required: true, message: '请输入科目', trigger: 'blur' }
  ],
  teacherId: [
    { required: true, message: '请选择老师', trigger: 'change' }
  ],
  classId: [
    { required: true, message: '请选择班级', trigger: 'change' }
  ],
  classroom: [
    { required: true, message: '请输入教室', trigger: 'blur' }
  ]
}

// 时间段配置
const timeSlots = [
  '08:00-08:45',
  '08:55-09:40',
  '10:00-10:45',
  '10:55-11:40',
  '14:00-14:45',
  '14:55-15:40',
  '16:00-16:45'
]

// 方法
const initScheduleData = () => {
  scheduleData.value = timeSlots.map(timeSlot => ({
    timeSlot,
    monday: [],
    tuesday: [],
    wednesday: [],
    thursday: [],
    friday: []
  }))
}

const fetchClasses = async () => {
  try {
    // TODO: 调用API获取班级列表
    classes.value = [
      { id: 1, name: '一年级1班' },
      { id: 2, name: '一年级2班' },
      { id: 3, name: '二年级1班' }
    ]
  } catch (error) {
    ElMessage.error('获取班级列表失败')
  }
}

const fetchTeachers = async () => {
  try {
    // TODO: 调用API获取老师列表
    teachers.value = [
      { id: 1, name: '张老师' },
      { id: 2, name: '李老师' },
      { id: 3, name: '王老师' }
    ]
  } catch (error) {
    ElMessage.error('获取老师列表失败')
  }
}

const fetchSchedule = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取课程表数据
    // 模拟数据
    const mockData = [
      {
        id: 1,
        subject: '数学',
        teacherId: 1,
        teacherName: '张老师',
        classId: 1,
        className: '一年级1班',
        classroom: '101',
        timeSlot: '08:00-08:45',
        dayOfWeek: 1
      }
    ]
    
    // 重置课程表数据
    initScheduleData()
    
    // 填充课程数据
    mockData.forEach(course => {
      const timeSlotIndex = scheduleData.value.findIndex(slot => slot.timeSlot === course.timeSlot)
      if (timeSlotIndex !== -1) {
        const dayKey = ['', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'][course.dayOfWeek]
        if (dayKey) {
          scheduleData.value[timeSlotIndex][dayKey].push(course)
        }
      }
    })
  } catch (error) {
    ElMessage.error('获取课程表失败')
  } finally {
    loading.value = false
  }
}

const handleWeekChange = () => {
  fetchSchedule()
}

const handleAutoSchedule = async () => {
  try {
    await ElMessageBox.confirm(
      '智能排课将自动安排所有课程，是否继续？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    ElMessage.success('智能排课功能待开发')
  } catch (error) {
    // 用户取消
  }
}

const handleAdd = () => {
  dialogTitle.value = '手动排课'
  resetForm()
  dialogVisible.value = true
}

const handleAddCourse = (timeSlot, dayOfWeek) => {
  dialogTitle.value = '添加课程'
  resetForm()
  form.timeSlot = timeSlot
  form.dayOfWeek = dayOfWeek
  dialogVisible.value = true
}

const handleEditCourse = (course) => {
  dialogTitle.value = '编辑课程'
  Object.assign(form, course)
  dialogVisible.value = true
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    ElMessage.success('保存成功')
    dialogVisible.value = false
    fetchSchedule()
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const handleDialogClose = () => {
  formRef.value?.resetFields()
}

const resetForm = () => {
  form.id = null
  form.subject = ''
  form.teacherId = ''
  form.classId = ''
  form.classroom = ''
  form.timeSlot = ''
  form.dayOfWeek = 1
}

// 生命周期
onMounted(() => {
  initScheduleData()
  fetchClasses()
  fetchTeachers()
  fetchSchedule()
})
</script>

<style scoped>
.schedule-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-bar {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-form {
  margin: 0;
}

.schedule-table-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.course-item {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 5px;
  cursor: pointer;
  transition: all 0.3s;
}

.course-item:hover {
  background: #dbeafe;
  border-color: #93c5fd;
}

.course-subject {
  font-weight: bold;
  color: #1e40af;
  margin-bottom: 2px;
}

.course-teacher {
  font-size: 12px;
  color: #6b7280;
}

.course-class {
  font-size: 12px;
  color: #6b7280;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
