<template>
  <div class="teachers-container">
    <div class="header">
      <h2>老师管理</h2>
      <!-- 只有校长才显示添加老师按钮 -->
      <el-button v-if="isPrincipal" type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>
        添加老师
      </el-button>
    </div>

    <div class="search-bar">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="姓名">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入老师姓名"
            clearable
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="科目">
          <el-select
            v-model="searchForm.subject"
            placeholder="请选择科目"
            clearable
          >
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="英语" value="英语" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
            <el-option label="生物" value="生物" />
          </el-select>
        </el-form-item>
        <!-- 管理员端显示校长筛选 -->
        <el-form-item v-if="isAdmin" label="所属校长">
          <el-select
            v-model="searchForm.principalId"
            placeholder="请选择校长"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="principal in principalList"
              :key="principal.userId"
              :label="principal.realName"
              :value="principal.userId"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-container">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <!-- 校长端在添加老师模式下显示多选框 -->
        <el-table-column
          v-if="isPrincipal && isAddingTeachers"
          type="selection"
          width="55"
        />
        <el-table-column prop="userId" label="ID" width="80" />
        <el-table-column prop="realName" label="姓名" />
        <el-table-column prop="phone" label="手机号" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="subject" label="主教科目" width="150">
          <template #default="scope">
            <template v-if="scope.row.subjectArray && Array.isArray(scope.row.subjectArray)">
              <el-tag
                v-for="(subj, index) in scope.row.subjectArray"
                :key="index"
                type="info"
                size="small"
                style="margin-right: 4px; margin-bottom: 2px;"
              >
                {{ subj }}
              </el-tag>
            </template>
            <template v-else-if="Array.isArray(scope.row.subject)">
              <el-tag
                v-for="(subj, index) in scope.row.subject"
                :key="index"
                type="info"
                size="small"
                style="margin-right: 4px; margin-bottom: 2px;"
              >
                {{ subj }}
              </el-tag>
            </template>
            <template v-else-if="scope.row.subject">
              <el-tag type="info" size="small">{{ scope.row.subject }}</el-tag>
            </template>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="experience" label="教学经验" width="100">
          <template #default="scope">
            {{ scope.row.experience }}年
          </template>
        </el-table-column>
        <!-- 管理员端显示校长信息 -->
        <el-table-column v-if="isAdmin" prop="principalName" label="所属校长" width="120">
          <template #default="scope">
            <span v-if="scope.row.principal">{{ scope.row.principal.realName }}</span>
            <el-tag v-else type="warning" size="small">未分配</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="schoolName" label="学校" width="150" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
              {{ scope.row.status === 'active' ? '在职' : '离职' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="入职时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <!-- 在添加老师模式下不显示操作按钮 -->
            <template v-if="!isAddingTeachers">
              <el-button
                v-if="isAdmin"
                type="primary"
                size="small"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                v-if="isAdmin"
                type="warning"
                size="small"
                @click="handleChangePrincipal(scope.row)"
              >
                调整校长
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="handleViewSchedule(scope.row)"
              >
                课表
              </el-button>
              <el-button
                type="danger"
                size="small"
                @click="handleDelete(scope.row)"
              >
                {{ isPrincipal ? '移除' : '删除' }}
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      :width="isAddingTeachers ? '800px' : '600px'"
      @close="handleDialogClose"
    >
      <!-- 校长添加老师：显示未分配老师列表 -->
      <div v-if="isAddingTeachers">
        <div style="margin-bottom: 16px;">
          <el-alert
            title="请选择要分配给您的老师"
            type="info"
            :closable="false"
            show-icon
          />
        </div>

        <!-- 搜索框 -->
        <div style="margin-bottom: 16px;">
          <el-input
            v-model="addTeacherSearchName"
            placeholder="搜索老师姓名"
            clearable
            style="width: 300px;"
            @input="handleAddTeacherSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <!-- 未分配老师表格 -->
        <el-table
          :data="addTeacherTableData"
          v-loading="addTeacherLoading"
          border
          style="width: 100%"
          max-height="400px"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="userId" label="ID" width="80" />
          <el-table-column prop="realName" label="姓名" width="120" />
          <el-table-column prop="phone" label="手机号" width="130" />
          <el-table-column prop="email" label="邮箱" width="180" />
          <el-table-column prop="subject" label="主教科目" width="120">
            <template #default="scope">
              <template v-if="scope.row.subjectArray && Array.isArray(scope.row.subjectArray)">
                <el-tag
                  v-for="(subj, index) in scope.row.subjectArray.slice(0, 2)"
                  :key="index"
                  type="info"
                  size="small"
                  style="margin-right: 2px; margin-bottom: 2px;"
                >
                  {{ subj }}
                </el-tag>
                <span v-if="scope.row.subjectArray.length > 2" style="font-size: 12px; color: #909399;">
                  +{{ scope.row.subjectArray.length - 2 }}
                </span>
              </template>
              <template v-else-if="Array.isArray(scope.row.subject)">
                <el-tag
                  v-for="(subj, index) in scope.row.subject.slice(0, 2)"
                  :key="index"
                  type="info"
                  size="small"
                  style="margin-right: 2px; margin-bottom: 2px;"
                >
                  {{ subj }}
                </el-tag>
                <span v-if="scope.row.subject.length > 2" style="font-size: 12px; color: #909399;">
                  +{{ scope.row.subject.length - 2 }}
                </span>
              </template>
              <template v-else-if="scope.row.subject">
                <el-tag type="info" size="small">{{ scope.row.subject }}</el-tag>
              </template>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column prop="experience" label="教学经验" width="100">
            <template #default="scope">
              {{ scope.row.experience || 0 }}年
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div style="margin-top: 16px; text-align: center;">
          <el-pagination
            v-model:current-page="addTeacherPagination.page"
            v-model:page-size="addTeacherPagination.size"
            :page-sizes="[5, 10, 20]"
            :total="addTeacherPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleAddTeacherSizeChange"
            @current-change="handleAddTeacherCurrentChange"
          />
        </div>
      </div>

      <!-- 管理员编辑老师：显示表单 -->
      <el-form
        v-else
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="form.name" placeholder="请输入老师姓名" />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入手机号" />
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入邮箱" />
        </el-form-item>
        <el-form-item label="主教科目" prop="subject">
          <el-select
            v-model="form.subject"
            placeholder="请选择科目（可多选）"
            style="width: 100%"
            multiple
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option label="语文" value="语文" />
            <el-option label="数学" value="数学" />
            <el-option label="英语" value="英语" />
            <el-option label="物理" value="物理" />
            <el-option label="化学" value="化学" />
            <el-option label="生物" value="生物" />
            <el-option label="历史" value="历史" />
            <el-option label="地理" value="地理" />
            <el-option label="政治" value="政治" />
          </el-select>
        </el-form-item>
        <el-form-item label="教学经验" prop="experience">
          <el-input-number
            v-model="form.experience"
            :min="0"
            :max="50"
            placeholder="请输入教学经验年数"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">在职</el-radio>
            <el-radio :label="0">离职</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button
            v-if="isAddingTeachers"
            type="primary"
            @click="handleAssignTeachers"
            :disabled="selectedTeachers.length === 0"
          >
            确定分配 ({{ selectedTeachers.length }})
          </el-button>
          <el-button
            v-else
            type="primary"
            @click="handleSubmit"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 管理员调整校长对话框 -->
    <el-dialog
      v-model="assignDialogVisible"
      title="调整老师所属校长"
      width="500px"
    >
      <el-form label-width="100px">
        <el-form-item label="老师姓名">
          <el-input :value="currentTeacher?.realName" disabled />
        </el-form-item>
        <el-form-item label="当前校长">
          <el-input :value="currentTeacher?.principal?.realName || '未分配'" disabled />
        </el-form-item>
        <el-form-item label="新校长">
          <el-select v-model="selectedPrincipalId" placeholder="请选择校长" clearable style="width: 100%">
            <el-option
              v-for="principal in principalList"
              :key="principal.userId"
              :label="principal.realName"
              :value="principal.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmChangePrincipal">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Search } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { principalTeacherApi, adminTeacherApi } from '@/api/teacher'
import { principalApi } from '@/api/principal'

// 用户状态
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const dialogVisible = ref(false)
const dialogTitle = ref('')
const formRef = ref()

// 校长选择相关
const principalList = ref([])
const selectedTeachers = ref([])
const assignDialogVisible = ref(false)

// 添加老师相关
const isAddingTeachers = ref(false)
const addTeacherSearchName = ref('')
const addTeacherTableData = ref([]) // 对话框中的未分配老师数据
const addTeacherLoading = ref(false) // 对话框中的加载状态
const addTeacherPagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 计算属性
const isAdmin = computed(() => userStore.isAdmin)
const isPrincipal = computed(() => userStore.isPrincipal)

// 搜索表单
const searchForm = reactive({
  name: '',
  subject: '',
  principalId: null // 管理员端用于筛选校长
})

// 当前操作的老师和校长
const currentTeacher = ref(null)
const selectedPrincipalId = ref(null)

// 分页
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 表单数据
const form = reactive({
  id: null,
  name: '',
  phone: '',
  email: '',
  subject: [],  // 改为数组支持多选
  experience: 0,
  status: 1
})

// 表单验证规则
const rules = {
  name: [
    { required: true, message: '请输入老师姓名', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  subject: [
    { required: true, message: '请至少选择一个主教科目', trigger: 'change' }
  ],
  experience: [
    { required: true, message: '请输入教学经验年数', trigger: 'blur' }
  ]
}

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    let response
    if (isPrincipal.value) {
      // 校长端：获取当前校长名下的老师列表
      response = await principalTeacherApi.getMyTeachers({
        page: pagination.page,
        size: pagination.size,
        realName: searchForm.name
      })
      pagination.total = response.data.total
    } else {
      // 管理员端：获取所有老师列表（包含校长信息）
      response = await adminTeacherApi.getTeacherList({
        page: pagination.page,
        size: pagination.size,
        realName: searchForm.name,
        principalId: searchForm.principalId
      })
      pagination.total = response.data.total
    }

    tableData.value = response.data.records
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 获取未分配老师数据（对话框专用）
const fetchUnassignedTeachers = async () => {
  addTeacherLoading.value = true
  try {
    const response = await principalTeacherApi.getUnassignedTeachers({
      page: addTeacherPagination.page,
      size: addTeacherPagination.size,
      realName: addTeacherSearchName.value
    })
    addTeacherTableData.value = response.data.records
    addTeacherPagination.total = response.data.total
  } catch (error) {
    console.error('获取未分配老师失败:', error)
    ElMessage.error('获取未分配老师失败')
  } finally {
    addTeacherLoading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  searchForm.name = ''
  searchForm.subject = ''
  searchForm.principalId = null
  pagination.page = 1
  fetchData()
}

const handleAdd = () => {
  dialogTitle.value = '添加老师'
  if (isPrincipal.value) {
    // 校长端：显示未分配校长的老师列表供选择
    isAddingTeachers.value = true
    selectedTeachers.value = []
    addTeacherSearchName.value = ''
    addTeacherPagination.page = 1
    addTeacherPagination.size = 10
    fetchUnassignedTeachers() // 获取未分配的老师列表
    dialogVisible.value = true
  } else {
    // 管理员端：显示创建老师表单
    isAddingTeachers.value = false
    resetForm()
    dialogVisible.value = true
  }
}

const handleEdit = async (row) => {
  dialogTitle.value = '编辑老师'
  isAddingTeachers.value = false

  try {
    // 获取老师的详细信息
    const response = await adminTeacherApi.getTeacherById(row.userId)
    const teacherInfo = response.data

    // 正确映射字段名并处理科目数据
    form.id = teacherInfo.userId
    form.name = teacherInfo.realName || ''
    form.phone = teacherInfo.phone || ''
    form.email = teacherInfo.email || ''
    form.experience = teacherInfo.experience || 0
    form.status = teacherInfo.status === 'active' ? 1 : 0

    // 处理科目数据 - 优先使用subjectArray，兼容subject字段
    if (teacherInfo.subjectArray && Array.isArray(teacherInfo.subjectArray)) {
      form.subject = [...teacherInfo.subjectArray]
    } else if (teacherInfo.subject) {
      if (Array.isArray(teacherInfo.subject)) {
        form.subject = [...teacherInfo.subject]
      } else if (typeof teacherInfo.subject === 'string') {
        // 如果是字符串，尝试按逗号分割
        form.subject = teacherInfo.subject.includes(',')
          ? teacherInfo.subject.split(',').map(s => s.trim())
          : [teacherInfo.subject]
      }
    } else {
      form.subject = []
    }

    dialogVisible.value = true
  } catch (error) {
    console.error('获取老师详细信息失败:', error)
    ElMessage.error('获取老师信息失败')
  }
}

const handleViewSchedule = (row) => {
  ElMessage.info(`查看${row.name}的课表功能待开发`)
}

const handleDelete = async (row) => {
  try {
    if (isPrincipal.value) {
      // 校长端：解除老师关系
      await ElMessageBox.confirm(
        `确定要将老师"${row.realName}"从您的名下移除吗？移除后该老师将变为未分配状态。`,
        '解除老师关系',
        {
          confirmButtonText: '确定移除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )

      await principalTeacherApi.removeTeacher(row.userId)
      ElMessage.success('老师关系解除成功')
    } else {
      // 管理员端：真正删除老师账号
      await ElMessageBox.confirm(
        `确定要删除老师"${row.realName}"吗？此操作将永久删除该老师账号，无法恢复！`,
        '删除老师账号',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'error'
        }
      )

      // TODO: 调用管理员删除老师的API
      ElMessage.success('老师删除成功')
    }

    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      const action = isPrincipal.value ? '解除老师关系' : '删除老师'
      ElMessage.error(`${action}失败`)
    }
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value.validate()

    // 准备提交数据
    const submitData = {
      realName: form.name,
      phone: form.phone,
      email: form.email,
      subject: form.subject, // 多选科目数组
      experience: form.experience,
      status: form.status === 1 ? 'active' : 'inactive'
    }

    if (form.id) {
      // 编辑模式
      await adminTeacherApi.updateTeacher(form.id, submitData)
      ElMessage.success('老师信息更新成功')
    } else {
      // 新增模式 - 这里可能需要调用创建接口
      ElMessage.info('新增功能待实现')
    }

    dialogVisible.value = false
    fetchData()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 处理对话框关闭
const handleDialogClose = () => {
  dialogVisible.value = false
  isAddingTeachers.value = false
  selectedTeachers.value = []
  addTeacherSearchName.value = ''
  addTeacherTableData.value = [] // 清空对话框数据
  formRef.value?.resetFields()
}

// 处理添加老师搜索
const handleAddTeacherSearch = () => {
  addTeacherPagination.page = 1
  fetchUnassignedTeachers()
}

// 处理添加老师分页大小变化
const handleAddTeacherSizeChange = (size) => {
  addTeacherPagination.size = size
  addTeacherPagination.page = 1
  fetchUnassignedTeachers()
}

// 处理添加老师页码变化
const handleAddTeacherCurrentChange = (page) => {
  addTeacherPagination.page = page
  fetchUnassignedTeachers()
}

const resetForm = () => {
  form.id = null
  form.name = ''
  form.phone = ''
  form.email = ''
  form.subject = []  // 重置为空数组
  form.experience = 0
  form.status = 1
}

const handleSizeChange = (size) => {
  pagination.size = size
  fetchData()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  fetchData()
}

// 处理表格选择变化
const handleSelectionChange = (selection) => {
  selectedTeachers.value = selection
}

// 处理分配老师
const handleAssignTeachers = async () => {
  if (selectedTeachers.value.length === 0) {
    ElMessage.warning('请选择要分配的老师')
    return
  }

  try {
    const teacherIds = selectedTeachers.value.map(teacher => teacher.userId)
    await principalTeacherApi.assignTeachers({ teacherIds })
    ElMessage.success(`成功分配 ${selectedTeachers.value.length} 位老师`)
    handleDialogClose()
    // 重新获取当前校长的老师列表
    fetchData()
  } catch (error) {
    console.error('分配老师失败:', error)
    ElMessage.error('分配老师失败')
  }
}

// 处理调整校长
const handleChangePrincipal = (row) => {
  currentTeacher.value = row
  selectedPrincipalId.value = row.principalId
  fetchPrincipalList()
  assignDialogVisible.value = true
}

// 确认调整校长
const handleConfirmChangePrincipal = async () => {
  try {
    await adminTeacherApi.updateTeacherPrincipal(
      currentTeacher.value.userId,
      selectedPrincipalId.value
    )
    ElMessage.success('校长调整成功')
    assignDialogVisible.value = false
    fetchData()
  } catch (error) {
    console.error('调整校长失败:', error)
    ElMessage.error('调整校长失败')
  }
}

// 获取校长列表
const fetchPrincipalList = async () => {
  try {
    const response = await principalApi.getList({
      page: 1,
      size: 100,
      schoolId: currentTeacher.value?.schoolId
    })
    principalList.value = response.data.records
  } catch (error) {
    console.error('获取校长列表失败:', error)
    ElMessage.error('获取校长列表失败')
  }
}

// 生命周期
onMounted(() => {
  fetchData()
  // 管理员端需要加载校长列表用于筛选
  if (isAdmin.value) {
    fetchAllPrincipals()
  }
})

// 获取所有校长列表（用于筛选）
const fetchAllPrincipals = async () => {
  try {
    const response = await principalApi.getList({
      page: 1,
      size: 100
    })
    principalList.value = response.data.records
  } catch (error) {
    console.error('获取校长列表失败:', error)
  }
}
</script>

<style scoped>
.teachers-container {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: #303133;
}

.search-bar {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin: 0;
}

.table-container {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
