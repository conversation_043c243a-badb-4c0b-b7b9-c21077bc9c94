<template>
  <div class="dashboard">
    <!-- 欢迎信息 -->
    <div class="welcome-card animate-fadeInUp">
      <div class="welcome-content">
        <h2 class="welcome-title">
          欢迎回来，{{ userStore.userName }}！
        </h2>
        <p class="welcome-subtitle">
          {{ getRoleText(userStore.userInfo?.role) }}
          <span v-if="userStore.schoolName"> - {{ userStore.schoolName }}</span>
        </p>
        <p class="welcome-time">{{ getCurrentTime() }}</p>
      </div>
      <div class="welcome-avatar">
        <el-avatar :size="80">
          <el-icon><User /></el-icon>
        </el-avatar>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div
        class="stat-card animate-fadeInUp hover-lift"
        v-for="(stat, index) in stats"
        :key="stat.title"
        :style="{
          '--card-color': stat.color,
          '--card-color-light': stat.color + '40',
          'animation-delay': `${index * 0.1}s`
        }"
      >
        <div class="stat-icon" :style="{ backgroundColor: stat.color }">
          <el-icon :size="24">
            <component :is="stat.icon" />
          </el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stat.value }}</div>
          <div class="stat-title">{{ stat.title }}</div>
        </div>
      </div>
    </div>
    
    <!-- 快捷操作 -->
    <div class="quick-actions animate-fadeInUp" style="animation-delay: 0.4s">
      <h3 class="section-title">快捷操作</h3>
      <div class="actions-grid">
        <div
          class="action-item hover-lift"
          v-for="(action, index) in quickActions"
          :key="action.title"
          @click="handleAction(action)"
          :style="{ 'animation-delay': `${0.5 + index * 0.1}s` }"
        >
          <el-icon :size="32" :color="action.color">
            <component :is="action.icon" />
          </el-icon>
          <span class="action-title">{{ action.title }}</span>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activities animate-fadeInUp" style="animation-delay: 0.6s">
      <h3 class="section-title">最近活动</h3>
      <div class="activity-list">
        <div
          class="activity-item animate-fadeInLeft"
          v-for="(activity, index) in activities"
          :key="activity.id"
          :style="{ 'animation-delay': `${0.7 + index * 0.1}s` }"
        >
          <div class="activity-icon">
            <el-icon :color="activity.color">
              <component :is="activity.icon" />
            </el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-time">{{ activity.time }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useUserStore()

// 统计数据
const stats = ref([
  { title: '今日课程', value: '8', icon: 'Calendar', color: '#409eff' },
  { title: '本月课时', value: '156', icon: 'Clock', color: '#67c23a' },
  { title: '学生总数', value: '45', icon: 'UserFilled', color: '#e6a23c' },
  { title: '待处理', value: '3', icon: 'Bell', color: '#f56c6c' }
])

// 快捷操作
const quickActions = computed(() => {
  const role = userStore.userInfo?.role
  const actions = []
  
  if (role === 'super_admin') {
    actions.push(
      { title: '学校管理', icon: 'School', color: '#409eff', path: '/admin/schools' },
      { title: '用户管理', icon: 'UserFilled', color: '#67c23a', path: '/admin/users' },
      { title: '系统设置', icon: 'Setting', color: '#e6a23c', path: '/profile/settings' }
    )
  } else if (role === 'principal') {
    actions.push(
      { title: '排课管理', icon: 'Calendar', color: '#409eff', path: '/principal/schedule' },
      { title: '老师管理', icon: 'User', color: '#67c23a', path: '/principal/teachers' },
      { title: '学生管理', icon: 'UserFilled', color: '#e6a23c', path: '/principal/students' },
      { title: '约课中心', icon: 'Clock', color: '#f56c6c', path: '/principal/bookings' }
    )
  } else if (role === 'teacher') {
    actions.push(
      { title: '我的课表', icon: 'Calendar', color: '#409eff', path: '/teacher/schedule' },
      { title: '考勤记录', icon: 'Clock', color: '#67c23a', path: '/teacher/attendance' },
      { title: '我的学生', icon: 'UserFilled', color: '#e6a23c', path: '/teacher/students' },
      { title: '工资查询', icon: 'Money', color: '#f56c6c', path: '/teacher/salary' }
    )
  }
  
  return actions
})

// 最近活动
const activities = ref([
  {
    id: 1,
    title: '张三同学完成了数学课程',
    time: '2小时前',
    icon: 'Check',
    color: '#67c23a'
  },
  {
    id: 2,
    title: '李老师提交了考勤记录',
    time: '4小时前',
    icon: 'Clock',
    color: '#409eff'
  },
  {
    id: 3,
    title: '新增约课请求待处理',
    time: '6小时前',
    icon: 'Bell',
    color: '#e6a23c'
  },
  {
    id: 4,
    title: '王五同学缴费成功',
    time: '1天前',
    icon: 'Money',
    color: '#67c23a'
  }
])

// 获取角色文本
const getRoleText = (role?: string) => {
  const roleMap: Record<string, string> = {
    super_admin: '超级管理员',
    principal: '校长',
    teacher: '老师'
  }
  return roleMap[role || ''] || '用户'
}

// 获取当前时间
const getCurrentTime = () => {
  return dayjs().format('YYYY年MM月DD日 dddd')
}

// 处理快捷操作
const handleAction = (action: any) => {
  if (action.path) {
    router.push(action.path)
  }
}

// 加载数据
const loadData = async () => {
  // TODO: 根据用户角色加载对应的统计数据
  console.log('加载仪表板数据')
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.dashboard {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  border-radius: 20px;
  padding: 40px;
  color: white;
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow:
    0 20px 40px rgba(102, 126, 234, 0.3),
    0 10px 20px rgba(118, 75, 162, 0.2);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.welcome-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  background: linear-gradient(45deg, #fff, #f0f8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 18px;
  opacity: 0.95;
  margin: 0 0 8px 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.welcome-time {
  font-size: 14px;
  opacity: 0.85;
  margin: 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.welcome-avatar {
  flex-shrink: 0;
  position: relative;
  z-index: 2;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--card-color, #667eea), var(--card-color-light, #764ba2));
}

.stat-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.15),
    0 8px 24px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  position: relative;
}

.stat-icon::after {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 12px;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), transparent);
}

.stat-value {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
  background: linear-gradient(45deg, #333, #666);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-title {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: #333;
  margin: 0 0 20px 0;
  position: relative;
  padding-left: 16px;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 2px;
}

.quick-actions {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 24px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 24px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.action-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.action-item:hover::before {
  opacity: 1;
}

.action-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateY(-4px) scale(1.05);
  box-shadow:
    0 12px 32px rgba(0, 0, 0, 0.15),
    0 6px 16px rgba(0, 0, 0, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
}

.action-title {
  font-size: 14px;
  color: #333;
  text-align: center;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.recent-activities {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 32px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    0 4px 16px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.activity-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.activity-item:hover::before {
  opacity: 1;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.8);
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: rgba(102, 126, 234, 0.2);
}

.activity-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.activity-title {
  font-size: 15px;
  color: #333;
  margin-bottom: 4px;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.activity-time {
  font-size: 13px;
  color: #666;
  position: relative;
  z-index: 1;
}

@media (max-width: 768px) {
  .welcome-card {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .action-item {
    padding: 16px;
  }
}
</style>
