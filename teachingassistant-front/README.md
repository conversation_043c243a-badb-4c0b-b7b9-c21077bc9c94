# 助教排课系统前端

## 项目简介

助教排课系统前端是基于Vue 3 + TypeScript + Element Plus构建的现代化管理系统界面，支持多角色权限管理、响应式设计。

## 技术栈

- **框架**: Vue 3.5.18
- **构建工具**: Vite 7.0.6
- **语言**: TypeScript 5.7.2
- **UI框架**: Element Plus 2.9.1
- **状态管理**: Pinia 2.3.0
- **路由**: Vue Router 4.5.0
- **HTTP客户端**: Axios 1.7.9
- **时间处理**: Day.js 1.11.13
- **进度条**: NProgress 0.2.0

## 功能特性

### 多角色权限体系
- **超级管理员**: 学校管理、校长管理、用户管理
- **校长**: 人员管理、教学管理、运营管理、财务管理
- **老师**: 课程表查看、考勤记录、学生管理、工资查询

### 核心功能
1. **用户认证**: JWT Token认证、自动刷新
2. **权限控制**: 基于角色的路由权限控制
3. **响应式设计**: 支持桌面端和移动端
4. **主题切换**: 支持明暗主题切换
5. **国际化**: 支持多语言切换
6. **状态管理**: 全局状态管理和持久化

## 快速开始

### 环境要求

- Node.js 20.19.0+ 或 22.12.0+
- npm 或 yarn 或 pnpm

### 安装依赖

```bash
# 使用npm
npm install

# 使用yarn
yarn install

# 使用pnpm
pnpm install
```

### 开发环境

```bash
# 启动开发服务器
npm run dev

# 或者
yarn dev
pnpm dev
```

访问地址：http://localhost:5173

### 构建生产版本

```bash
# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

### 类型检查

```bash
# 运行TypeScript类型检查
npm run type-check
```
