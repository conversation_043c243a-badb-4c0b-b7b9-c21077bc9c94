{"name": "teachingassistant-front", "version": "1.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.5.18", "vue-router": "^4.5.0", "pinia": "^2.3.0", "element-plus": "^2.9.1", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.9", "dayjs": "^1.11.13", "nprogress": "^0.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "@types/node": "^22.10.2", "@types/js-cookie": "^3.0.6", "@types/nprogress": "^0.2.3", "typescript": "^5.7.2", "vue-tsc": "^2.1.10", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "unplugin-auto-import": "^0.18.6", "unplugin-vue-components": "^0.27.4"}}