# 助教排课系统 - 快速启动指南

## 环境准备

### 必需软件
1. **JDK 17+** - 后端运行环境
2. **Node.js 20.19.0+** - 前端运行环境
3. **MySQL 8.0+** - 数据库
4. **Redis 5.0+** - 缓存（可选）
5. **Maven 3.6+** - 后端构建工具

### 软件下载地址
- JDK 17: https://adoptium.net/
- Node.js: https://nodejs.org/
- MySQL: https://dev.mysql.com/downloads/mysql/
- Redis: https://redis.io/download
- Maven: https://maven.apache.org/download.cgi

## 快速启动步骤

### 第一步：数据库初始化

1. 启动MySQL服务
2. 使用MySQL客户端执行 `init-database.sql` 脚本：
```sql
source init-database.sql
```

或者手动创建数据库：
```sql
CREATE DATABASE teaching_assistant CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 第二步：配置后端

1. 打开 `teachingassistant-backend/src/main/resources/application.yaml`
2. 修改数据库连接配置：
```yaml
spring:
  datasource:
    url: ************************************************************************************************************************************************************
    username: root
    password: 你的MySQL密码
```

3. 如果有Redis，修改Redis配置：
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      password: 你的Redis密码
```

### 第三步：启动后端服务

**方式一：使用启动脚本（推荐）**
```bash
# Windows
start-backend.bat

# Linux/Mac
chmod +x start-backend.sh
./start-backend.sh
```

**方式二：手动启动**
```bash
cd teachingassistant-backend
mvn clean compile
mvn flyway:migrate
mvn spring-boot:run
```

启动成功后访问：http://localhost:8080/api/test/health

### 第四步：启动前端服务

**方式一：使用启动脚本（推荐）**
```bash
# Windows
start-frontend.bat

# Linux/Mac
chmod +x start-frontend.sh
./start-frontend.sh
```

**方式二：手动启动**
```bash
cd teachingassistant-front
npm install
npm run dev
```

启动成功后访问：http://localhost:5173

## 登录测试

### 超级管理员
- 用户名：`admin`
- 密码：`admin123`
- 功能：学校管理、用户管理

### 校长
- 用户名：`principal001`
- 密码：`admin123`
- 功能：人员管理、教学管理、运营管理

### 老师
- 用户名：`teacher001`
- 密码：`admin123`
- 功能：课程表、考勤记录、学生管理

## 常见问题解决

### 1. 后端启动失败

**问题：数据库连接失败**
- 检查MySQL服务是否启动
- 确认数据库连接配置是否正确
- 确认数据库用户权限

**问题：端口被占用**
- 修改 `application.yaml` 中的 `server.port`
- 或者停止占用8080端口的程序

**问题：Maven依赖下载失败**
- 检查网络连接
- 配置Maven镜像源

### 2. 前端启动失败

**问题：Node.js版本不兼容**
- 升级Node.js到20.19.0+版本

**问题：依赖安装失败**
- 清除缓存：`npm cache clean --force`
- 删除node_modules重新安装
- 使用国内镜像：`npm config set registry https://registry.npmmirror.com`

**问题：端口被占用**
- 修改 `vite.config.js` 中的端口配置
- 或者停止占用5173端口的程序

### 3. 数据库问题

**问题：Flyway迁移失败**
- 检查数据库连接
- 确认数据库用户有创建表的权限
- 手动执行 `助教系统建表语句.sql`

**问题：中文乱码**
- 确认数据库字符集为utf8mb4
- 检查连接URL中的字符编码参数

## 开发环境配置

### IDE推荐
- **后端**：IntelliJ IDEA / Eclipse
- **前端**：VS Code + Volar插件

### 调试配置
1. 后端调试：在IDE中配置Spring Boot运行配置
2. 前端调试：使用浏览器开发者工具

### 热重载
- 后端：使用spring-boot-devtools
- 前端：Vite自动热重载

## 生产部署

### Docker部署
```bash
# 构建镜像
docker build -t teaching-assistant-backend ./teachingassistant-backend
docker build -t teaching-assistant-frontend ./teachingassistant-front

# 运行容器
docker run -d -p 8080:8080 teaching-assistant-backend
docker run -d -p 80:80 teaching-assistant-frontend
```

### 传统部署
```bash
# 后端打包
cd teachingassistant-backend
mvn clean package -DskipTests
java -jar target/teachingassistant-backend-1.0.0.jar

# 前端打包
cd teachingassistant-front
npm run build
# 将dist目录部署到Web服务器
```

## 技术支持

如遇到问题，请按以下顺序排查：
1. 检查环境要求是否满足
2. 查看控制台错误日志
3. 参考常见问题解决方案
4. 联系开发团队

---

**祝您使用愉快！**
