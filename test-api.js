// 简单的API测试脚本
// 使用Node.js运行: node test-api.js

const https = require('https');
const http = require('http');

// 测试API连通性
function testAPI() {
    console.log('🧪 开始测试教室管理API...\n');
    
    // 测试后端健康检查
    const options = {
        hostname: 'localhost',
        port: 8081,
        path: '/api/actuator/health',
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        }
    };
    
    const req = http.request(options, (res) => {
        console.log(`✅ 后端服务状态: ${res.statusCode}`);
        
        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });
        
        res.on('end', () => {
            try {
                const response = JSON.parse(data);
                console.log('📊 健康检查结果:', response);
                console.log('\n🎉 后端服务运行正常！');
                console.log('\n📝 接下来请手动测试以下功能：');
                console.log('1. 访问 http://localhost:3001');
                console.log('2. 登录校长账号');
                console.log('3. 导航到教室管理页面');
                console.log('4. 测试增删改查功能');
            } catch (e) {
                console.log('📊 服务响应:', data);
            }
        });
    });
    
    req.on('error', (e) => {
        console.error(`❌ 连接后端服务失败: ${e.message}`);
        console.log('请确保后端服务已启动在 http://localhost:8081');
    });
    
    req.end();
}

// 运行测试
testAPI();
