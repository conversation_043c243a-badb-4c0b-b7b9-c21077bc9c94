openapi: 3.0.3
info:
  title: 教室管理API
  description: 助教排课系统 - 教室管理模块API文档
  version: 1.0.0
  contact:
    name: Teaching Assistant System
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api
    description: 开发环境

tags:
  - name: 管理员教室管理
    description: 超级管理员的教室管理接口
  - name: 校长教室管理
    description: 校长的教室管理接口

paths:
  /admin/classrooms:
    get:
      tags:
        - 管理员教室管理
      summary: 获取教室列表（分页）
      description: 管理员获取所有学校的教室列表，支持分页和筛选
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: size
          in: query
          description: 每页数量
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
        - name: schoolId
          in: query
          description: 学校ID
          schema:
            type: integer
        - name: name
          in: query
          description: 教室名称（模糊搜索）
          schema:
            type: string
        - name: floor
          in: query
          description: 楼层
          schema:
            type: integer
            minimum: 1
            maximum: 50
        - name: type
          in: query
          description: 教室类型
          schema:
            type: string
            enum: [normal, multimedia, lab]
        - name: status
          in: query
          description: 教室状态
          schema:
            type: string
            enum: [available, in_use, maintenance]
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResultClassroom'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []

    post:
      tags:
        - 管理员教室管理
      summary: 创建教室
      description: 管理员创建新教室
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateClassroomRequest'
      responses:
        '200':
          description: 创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClassroomResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []

  /admin/classrooms/all:
    get:
      tags:
        - 管理员教室管理
      summary: 获取所有教室列表
      description: 管理员获取所有教室列表（不分页）
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClassroomListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []

  /admin/classrooms/{classroomId}:
    get:
      tags:
        - 管理员教室管理
      summary: 获取教室详情
      description: 管理员根据ID获取教室详细信息
      parameters:
        - name: classroomId
          in: path
          required: true
          description: 教室ID
          schema:
            type: integer
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClassroomResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []

    put:
      tags:
        - 管理员教室管理
      summary: 更新教室信息
      description: 管理员更新教室信息
      parameters:
        - name: classroomId
          in: path
          required: true
          description: 教室ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateClassroomRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClassroomResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []

    delete:
      tags:
        - 管理员教室管理
      summary: 删除教室
      description: 管理员删除教室
      parameters:
        - name: classroomId
          in: path
          required: true
          description: 教室ID
          schema:
            type: integer
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []

  /principal/classrooms:
    get:
      tags:
        - 校长教室管理
      summary: 获取教室列表（分页）
      description: 校长获取本校教室列表，支持分页和筛选
      parameters:
        - name: page
          in: query
          description: 页码
          schema:
            type: integer
            default: 1
            minimum: 1
        - name: size
          in: query
          description: 每页数量
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
        - name: name
          in: query
          description: 教室名称（模糊搜索）
          schema:
            type: string
        - name: floor
          in: query
          description: 楼层
          schema:
            type: integer
            minimum: 1
            maximum: 50
        - name: type
          in: query
          description: 教室类型
          schema:
            type: string
            enum: [normal, multimedia, lab]
        - name: status
          in: query
          description: 教室状态
          schema:
            type: string
            enum: [available, in_use, maintenance]
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PageResultClassroom'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []

    post:
      tags:
        - 校长教室管理
      summary: 创建教室
      description: 校长为本校创建新教室
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePrincipalClassroomRequest'
      responses:
        '200':
          description: 创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClassroomResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []

  /principal/classrooms/all:
    get:
      tags:
        - 校长教室管理
      summary: 获取所有教室列表
      description: 校长获取本校所有教室列表（不分页）
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClassroomListResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []

  /principal/classrooms/{classroomId}:
    get:
      tags:
        - 校长教室管理
      summary: 获取教室详情
      description: 校长根据ID获取本校教室详细信息
      parameters:
        - name: classroomId
          in: path
          required: true
          description: 教室ID
          schema:
            type: integer
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClassroomResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []

    put:
      tags:
        - 校长教室管理
      summary: 更新教室信息
      description: 校长更新本校教室信息
      parameters:
        - name: classroomId
          in: path
          required: true
          description: 教室ID
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateClassroomRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClassroomResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []

    delete:
      tags:
        - 校长教室管理
      summary: 删除教室
      description: 校长删除本校教室
      parameters:
        - name: classroomId
          in: path
          required: true
          description: 教室ID
          schema:
            type: integer
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
      security:
        - bearerAuth: []

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    Classroom:
      type: object
      properties:
        classroomId:
          type: integer
          description: 教室ID
        schoolId:
          type: integer
          description: 所属学校ID
        name:
          type: string
          description: 教室名称
        floor:
          type: integer
          description: 楼层
          minimum: 1
          maximum: 50
        type:
          type: string
          enum: [normal, multimedia, lab]
          description: 教室类型（normal-普通教室，multimedia-多媒体教室，lab-实验室）
        capacity:
          type: integer
          description: 容量（人数）
          minimum: 1
          maximum: 500
        equipment:
          type: string
          description: 设备描述
        status:
          type: string
          enum: [available, in_use, maintenance]
          description: 教室状态（available-可用，in_use-使用中，maintenance-维护中）
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间
        school:
          $ref: '#/components/schemas/School'

    School:
      type: object
      properties:
        schoolId:
          type: integer
          description: 学校ID
        name:
          type: string
          description: 学校名称
        address:
          type: string
          description: 学校地址
        status:
          type: string
          enum: [active, inactive]
          description: 学校状态

    CreateClassroomRequest:
      type: object
      required:
        - schoolId
        - name
        - type
        - capacity
      properties:
        schoolId:
          type: integer
          description: 所属学校ID
        name:
          type: string
          description: 教室名称
          maxLength: 50
        floor:
          type: integer
          description: 楼层
          minimum: 1
          maximum: 50
        type:
          type: string
          enum: [normal, multimedia, lab]
          description: 教室类型
          default: normal
        capacity:
          type: integer
          description: 容量（人数）
          minimum: 1
          maximum: 500
          default: 30
        equipment:
          type: string
          description: 设备描述
          maxLength: 500
        status:
          type: string
          enum: [available, in_use, maintenance]
          description: 教室状态
          default: available

    CreatePrincipalClassroomRequest:
      type: object
      required:
        - name
        - type
        - capacity
      properties:
        name:
          type: string
          description: 教室名称
          maxLength: 50
        floor:
          type: integer
          description: 楼层
          minimum: 1
          maximum: 50
        type:
          type: string
          enum: [normal, multimedia, lab]
          description: 教室类型
          default: normal
        capacity:
          type: integer
          description: 容量（人数）
          minimum: 1
          maximum: 500
          default: 30
        equipment:
          type: string
          description: 设备描述
          maxLength: 500
        status:
          type: string
          enum: [available, in_use, maintenance]
          description: 教室状态
          default: available

    UpdateClassroomRequest:
      type: object
      properties:
        name:
          type: string
          description: 教室名称
          maxLength: 50
        floor:
          type: integer
          description: 楼层
          minimum: 1
          maximum: 50
        type:
          type: string
          enum: [normal, multimedia, lab]
          description: 教室类型
        capacity:
          type: integer
          description: 容量（人数）
          minimum: 1
          maximum: 500
        equipment:
          type: string
          description: 设备描述
          maxLength: 500
        status:
          type: string
          enum: [available, in_use, maintenance]
          description: 教室状态

    PageResult:
      type: object
      properties:
        page:
          type: integer
          description: 当前页码
        size:
          type: integer
          description: 每页数量
        total:
          type: integer
          description: 总记录数
        pages:
          type: integer
          description: 总页数
        hasNext:
          type: boolean
          description: 是否有下一页
        hasPrevious:
          type: boolean
          description: 是否有上一页

    PageResultClassroom:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              allOf:
                - $ref: '#/components/schemas/PageResult'
                - type: object
                  properties:
                    records:
                      type: array
                      items:
                        $ref: '#/components/schemas/Classroom'

    ClassroomResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/Classroom'

    ClassroomListResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Classroom'

    SuccessResponse:
      $ref: '#/components/schemas/ApiResponse'

    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: 操作成功
        timestamp:
          type: integer
          description: 时间戳
          example: 1640995200000

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
        message:
          type: string
          description: 错误消息
        timestamp:
          type: integer
          description: 时间戳

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 400
            message: 请求参数错误
            timestamp: 1640995200000

    Unauthorized:
      description: 未授权
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 401
            message: 未授权，请重新登录
            timestamp: 1640995200000

    Forbidden:
      description: 权限不足
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 403
            message: 权限不足
            timestamp: 1640995200000

    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 404
            message: 教室不存在
            timestamp: 1640995200000
