openapi: 3.0.3
info:
  title: 用户设置管理API
  description: 助教系统用户设置管理相关接口文档
  version: 1.0.0
  contact:
    name: Teaching Assistant System
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: 开发环境
  - url: https://api.teachingassistant.com
    description: 生产环境

tags:
  - name: user-settings
    description: 用户设置管理

paths:
  /api/user/settings:
    get:
      tags:
        - user-settings
      summary: 获取当前用户设置
      description: 获取当前登录用户的个人设置信息
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserSettings'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      tags:
        - user-settings
      summary: 更新用户设置
      description: 更新当前登录用户的设置信息
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserSettingsRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserSettings'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /api/user/settings/theme:
    put:
      tags:
        - user-settings
      summary: 更新主题设置
      description: 单独更新用户的主题设置
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateThemeRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /api/user/settings/notification:
    put:
      tags:
        - user-settings
      summary: 更新通知设置
      description: 更新用户的通知相关设置
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                notificationEnabled:
                  type: boolean
                  description: 消息提醒开关
                classReminder:
                  type: boolean
                  description: 课程提醒开关
                appointmentReminder:
                  type: boolean
                  description: 约课提醒开关
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /api/user/settings/interface:
    put:
      tags:
        - user-settings
      summary: 更新界面设置
      description: 更新用户的界面相关设置
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                theme:
                  type: string
                  enum: [light, dark]
                  description: 界面主题
                language:
                  type: string
                  enum: [zh-CN, en-US]
                  description: 语言设置
                sidebarCollapsed:
                  type: boolean
                  description: 侧边栏折叠状态
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /api/user/settings/reset:
    post:
      tags:
        - user-settings
      summary: 重置为默认设置
      description: 将用户设置重置为系统默认值
      security:
        - bearerAuth: []
      responses:
        '200':
          description: 重置成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserSettings'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
          example: 200
        message:
          type: string
          description: 响应消息
          example: "操作成功"
        timestamp:
          type: string
          format: date-time
          description: 响应时间戳

    UserSettings:
      type: object
      properties:
        settingId:
          type: integer
          format: int64
          description: 设置ID
        userId:
          type: integer
          format: int64
          description: 用户ID
        notificationEnabled:
          type: boolean
          description: 消息提醒开关
          example: true
        theme:
          type: string
          enum: [light, dark]
          description: 界面主题
          example: "dark"
        language:
          type: string
          enum: [zh-CN, en-US]
          description: 语言设置
          example: "zh-CN"
        classReminder:
          type: boolean
          description: 课程提醒开关
          example: true
        appointmentReminder:
          type: boolean
          description: 约课提醒开关
          example: true
        sidebarCollapsed:
          type: boolean
          description: 侧边栏折叠状态
          example: false
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间

    UpdateUserSettingsRequest:
      type: object
      properties:
        notificationEnabled:
          type: boolean
          description: 消息提醒开关
        theme:
          type: string
          enum: [light, dark]
          description: 界面主题
        language:
          type: string
          enum: [zh-CN, en-US]
          description: 语言设置
        classReminder:
          type: boolean
          description: 课程提醒开关
        appointmentReminder:
          type: boolean
          description: 约课提醒开关
        sidebarCollapsed:
          type: boolean
          description: 侧边栏折叠状态

    UpdateThemeRequest:
      type: object
      required:
        - theme
      properties:
        theme:
          type: string
          enum: [light, dark]
          description: 界面主题
          example: "dark"

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
        message:
          type: string
          description: 错误消息
        timestamp:
          type: string
          format: date-time
          description: 错误时间戳

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 400
            message: "请求参数错误"
            timestamp: "2024-01-01T12:00:00Z"

    Unauthorized:
      description: 未授权访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 401
            message: "未授权访问"
            timestamp: "2024-01-01T12:00:00Z"

    InternalServerError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            code: 500
            message: "服务器内部错误"
            timestamp: "2024-01-01T12:00:00Z"
