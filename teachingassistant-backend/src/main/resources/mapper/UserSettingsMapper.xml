<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teachingassistant.mapper.UserSettingsMapper">

    <!-- 结果映射 -->
    <resultMap id="UserSettingsResultMap" type="com.teachingassistant.entity.UserSettings">
        <id column="setting_id" property="settingId"/>
        <result column="user_id" property="userId"/>
        <result column="notification_enabled" property="notificationEnabled"/>
        <result column="theme" property="theme"/>
        <result column="language" property="language"/>
        <result column="class_reminder" property="classReminder"/>
        <result column="appointment_reminder" property="appointmentReminder"/>
        <result column="sidebar_collapsed" property="sidebarCollapsed"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        setting_id, user_id, notification_enabled, theme, language, 
        class_reminder, appointment_reminder, sidebar_collapsed, created_at, updated_at
    </sql>

    <!-- 根据用户ID查询用户设置 -->
    <select id="findByUserId" resultMap="UserSettingsResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_settings
        WHERE user_id = #{userId}
    </select>

    <!-- 插入用户设置 -->
    <insert id="insert" parameterType="com.teachingassistant.entity.UserSettings" useGeneratedKeys="true" keyProperty="settingId">
        INSERT INTO user_settings (
            user_id, notification_enabled, theme, language, 
            class_reminder, appointment_reminder, sidebar_collapsed
        ) VALUES (
            #{userId}, #{notificationEnabled}, #{theme}, #{language},
            #{classReminder}, #{appointmentReminder}, #{sidebarCollapsed}
        )
    </insert>

    <!-- 更新用户设置 -->
    <update id="update" parameterType="com.teachingassistant.entity.UserSettings">
        UPDATE user_settings
        <set>
            <if test="notificationEnabled != null">notification_enabled = #{notificationEnabled},</if>
            <if test="theme != null">theme = #{theme},</if>
            <if test="language != null">language = #{language},</if>
            <if test="classReminder != null">class_reminder = #{classReminder},</if>
            <if test="appointmentReminder != null">appointment_reminder = #{appointmentReminder},</if>
            <if test="sidebarCollapsed != null">sidebar_collapsed = #{sidebarCollapsed},</if>
            updated_at = CURRENT_TIMESTAMP
        </set>
        WHERE user_id = #{userId}
    </update>

    <!-- 根据用户ID删除设置 -->
    <delete id="deleteByUserId">
        DELETE FROM user_settings WHERE user_id = #{userId}
    </delete>

    <!-- 检查用户设置是否存在 -->
    <select id="existsByUserId" resultType="boolean">
        SELECT COUNT(1) > 0 FROM user_settings WHERE user_id = #{userId}
    </select>

    <!-- 更新主题设置 -->
    <update id="updateTheme">
        UPDATE user_settings 
        SET theme = #{theme}, updated_at = CURRENT_TIMESTAMP
        WHERE user_id = #{userId}
    </update>

    <!-- 更新通知设置 -->
    <update id="updateNotificationSettings">
        UPDATE user_settings 
        SET notification_enabled = #{notificationEnabled},
            class_reminder = #{classReminder},
            appointment_reminder = #{appointmentReminder},
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = #{userId}
    </update>

    <!-- 更新界面设置 -->
    <update id="updateInterfaceSettings">
        UPDATE user_settings 
        SET theme = #{theme},
            language = #{language},
            sidebar_collapsed = #{sidebarCollapsed},
            updated_at = CURRENT_TIMESTAMP
        WHERE user_id = #{userId}
    </update>

</mapper>
