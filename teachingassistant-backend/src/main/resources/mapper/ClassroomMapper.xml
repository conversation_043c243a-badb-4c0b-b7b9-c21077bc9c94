<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teachingassistant.mapper.ClassroomMapper">

    <!-- 结果映射 -->
    <resultMap id="ClassroomResultMap" type="com.teachingassistant.entity.Classroom">
        <id property="classroomId" column="classroom_id"/>
        <result property="schoolId" column="school_id"/>
        <result property="name" column="name"/>
        <result property="floor" column="floor"/>
        <result property="type" column="type"/>
        <result property="capacity" column="capacity"/>
        <result property="equipment" column="equipment"/>
        <result property="status" column="status"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
    </resultMap>

    <!-- 包含学校信息的结果映射 -->
    <resultMap id="ClassroomWithSchoolResultMap" type="com.teachingassistant.entity.Classroom" extends="ClassroomResultMap">
        <association property="school" javaType="com.teachingassistant.entity.School">
            <id property="schoolId" column="s_school_id"/>
            <result property="name" column="s_name"/>
            <result property="address" column="s_address"/>
            <result property="status" column="s_status"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="BaseColumns">
        c.classroom_id, c.school_id, c.name, c.floor, c.type, c.capacity, 
        c.equipment, c.status, c.created_at, c.updated_at
    </sql>

    <!-- 包含学校信息的查询字段 -->
    <sql id="ClassroomWithSchoolColumns">
        <include refid="BaseColumns"/>,
        s.school_id as s_school_id, s.name as s_name, s.address as s_address, s.status as s_status
    </sql>

    <!-- 查询条件 -->
    <sql id="WhereConditions">
        <where>
            <if test="schoolId != null">
                AND c.school_id = #{schoolId}
            </if>
            <if test="name != null and name != ''">
                AND c.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="floor != null">
                AND c.floor = #{floor}
            </if>
            <if test="type != null and type != ''">
                AND c.type = #{type}
            </if>
            <if test="status != null and status != ''">
                AND c.status = #{status}
            </if>
        </where>
    </sql>

    <!-- 根据教室ID查询教室 -->
    <select id="findById" resultMap="ClassroomWithSchoolResultMap">
        SELECT <include refid="ClassroomWithSchoolColumns"/>
        FROM classrooms c
        LEFT JOIN schools s ON c.school_id = s.school_id
        WHERE c.classroom_id = #{classroomId}
    </select>

    <!-- 根据学校ID查询教室列表 -->
    <select id="findBySchoolId" resultMap="ClassroomResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM classrooms c
        WHERE c.school_id = #{schoolId}
        ORDER BY c.floor ASC, c.name ASC
    </select>

    <!-- 查询所有教室列表 -->
    <select id="findAll" resultMap="ClassroomWithSchoolResultMap">
        SELECT <include refid="ClassroomWithSchoolColumns"/>
        FROM classrooms c
        LEFT JOIN schools s ON c.school_id = s.school_id
        ORDER BY s.name ASC, c.floor ASC, c.name ASC
    </select>

    <!-- 分页查询教室列表（管理员端） -->
    <select id="findWithPagination" resultMap="ClassroomWithSchoolResultMap">
        SELECT <include refid="ClassroomWithSchoolColumns"/>
        FROM classrooms c
        LEFT JOIN schools s ON c.school_id = s.school_id
        <include refid="WhereConditions"/>
        ORDER BY s.name ASC, c.floor ASC, c.name ASC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 分页查询教室列表（校长端） -->
    <select id="findBySchoolWithPagination" resultMap="ClassroomResultMap">
        SELECT <include refid="BaseColumns"/>
        FROM classrooms c
        WHERE c.school_id = #{schoolId}
        <if test="name != null and name != ''">
            AND c.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="floor != null">
            AND c.floor = #{floor}
        </if>
        <if test="type != null and type != ''">
            AND c.type = #{type}
        </if>
        <if test="status != null and status != ''">
            AND c.status = #{status}
        </if>
        ORDER BY c.floor ASC, c.name ASC
        LIMIT #{offset}, #{size}
    </select>

    <!-- 统计教室总数（管理员端） -->
    <select id="countClassrooms" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM classrooms c
        <include refid="WhereConditions"/>
    </select>

    <!-- 统计教室总数（校长端） -->
    <select id="countBySchool" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM classrooms c
        WHERE c.school_id = #{schoolId}
        <if test="name != null and name != ''">
            AND c.name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="floor != null">
            AND c.floor = #{floor}
        </if>
        <if test="type != null and type != ''">
            AND c.type = #{type}
        </if>
        <if test="status != null and status != ''">
            AND c.status = #{status}
        </if>
    </select>

    <!-- 插入教室 -->
    <insert id="insert" useGeneratedKeys="true" keyProperty="classroomId">
        INSERT INTO classrooms (school_id, name, floor, type, capacity, equipment, status)
        VALUES (#{schoolId}, #{name}, #{floor}, #{type}, #{capacity}, #{equipment}, #{status})
    </insert>

    <!-- 更新教室信息 -->
    <update id="update">
        UPDATE classrooms
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="floor != null">
                floor = #{floor},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="capacity != null">
                capacity = #{capacity},
            </if>
            <if test="equipment != null">
                equipment = #{equipment},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            updated_at = CURRENT_TIMESTAMP
        </set>
        WHERE classroom_id = #{classroomId}
    </update>

    <!-- 根据ID删除教室 -->
    <delete id="deleteById">
        DELETE FROM classrooms WHERE classroom_id = #{classroomId}
    </delete>

    <!-- 检查教室名称是否在同一学校内存在 -->
    <select id="existsByNameAndSchoolId" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM classrooms
        WHERE name = #{name} AND school_id = #{schoolId}
    </select>

    <!-- 检查教室名称是否在同一学校内存在（排除指定ID） -->
    <select id="existsByNameAndSchoolIdExcludeId" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM classrooms
        WHERE name = #{name} AND school_id = #{schoolId} AND classroom_id != #{classroomId}
    </select>

</mapper>
