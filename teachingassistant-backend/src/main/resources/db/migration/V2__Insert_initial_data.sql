-- 插入初始数据

-- 插入超级管理员
INSERT INTO `users` (`username`, `password`, `role`, `real_name`, `phone`, `status`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b8sO/Y2ufgdgLu', 'super_admin', '系统管理员', '13800000000', 'active');
-- 密码是: admin123

-- 插入示例学校
INSERT INTO `schools` (`name`, `address`, `admin_quota`, `status`) VALUES
('北京市第一中学', '北京市朝阳区建国路1号', 2, 'active'),
('上海市实验中学', '上海市浦东新区世纪大道100号', 1, 'active'),
('广州市育才学校', '广州市天河区珠江新城', 1, 'active');

-- 插入校长用户
INSERT INTO `users` (`school_id`, `username`, `password`, `role`, `real_name`, `phone`, `status`) VALUES
(1, 'principal001', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b8sO/Y2ufgdgLu', 'principal', '张校长', '13800000001', 'active'),
(2, 'principal002', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b8sO/Y2ufgdgLu', 'principal', '李校长', '13800000002', 'active'),
(3, 'principal003', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b8sO/Y2ufgdgLu', 'principal', '王校长', '13800000003', 'active');
-- 密码都是: admin123

-- 插入老师用户
INSERT INTO `users` (`school_id`, `username`, `password`, `role`, `real_name`, `phone`, `status`) VALUES
(1, 'teacher001', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b8sO/Y2ufgdgLu', 'teacher', '张老师', '13800001001', 'active'),
(1, 'teacher002', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b8sO/Y2ufgdgLu', 'teacher', '李老师', '13800001002', 'active'),
(1, 'teacher003', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b8sO/Y2ufgdgLu', 'teacher', '王老师', '13800001003', 'active'),
(2, 'teacher004', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b8sO/Y2ufgdgLu', 'teacher', '赵老师', '13800002001', 'active'),
(2, 'teacher005', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b8sO/Y2ufgdgLu', 'teacher', '钱老师', '13800002002', 'active'),
(3, 'teacher006', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9b8sO/Y2ufgdgLu', 'teacher', '孙老师', '13800003001', 'active');

-- 插入班级
INSERT INTO `classes` (`school_id`, `name`, `main_teacher`) VALUES
(1, '高一(1)班', 5),
(1, '高一(2)班', 6),
(1, '高二(1)班', 7),
(2, '初三(1)班', 8),
(2, '初三(2)班', 9),
(3, '高三(1)班', 10);

-- 插入教室
INSERT INTO `classrooms` (`school_id`, `name`, `status`) VALUES
(1, '101教室', 'available'),
(1, '102教室', 'available'),
(1, '103教室', 'available'),
(1, '物理实验室', 'available'),
(1, '化学实验室', 'available'),
(2, '201教室', 'available'),
(2, '202教室', 'available'),
(2, '203教室', 'available'),
(3, '301教室', 'available'),
(3, '302教室', 'available');

-- 插入学生
INSERT INTO `students` (`school_id`, `name`, `class_id`, `contact_phone`, `unpaid_amount`) VALUES
(1, '张三', 1, '13900001001', 0.00),
(1, '李四', 1, '13900001002', 500.00),
(1, '王五', 1, '13900001003', 0.00),
(1, '赵六', 2, '13900001004', 300.00),
(1, '钱七', 2, '13900001005', 0.00),
(1, '孙八', 3, '13900001006', 800.00),
(2, '周九', 4, '13900002001', 0.00),
(2, '吴十', 4, '13900002002', 200.00),
(2, '郑一', 5, '13900002003', 0.00),
(3, '王二', 6, '13900003001', 1000.00);

-- 插入师生关联关系
INSERT INTO `teacher_student` (`teacher_id`, `student_id`) VALUES
(5, 1), (5, 2), (5, 3),
(6, 4), (6, 5),
(7, 6),
(8, 7), (8, 8),
(9, 9),
(10, 10);

-- 插入用户设置
INSERT INTO `user_settings` (`user_id`, `notification_enabled`, `theme`, `language`) VALUES
(1, TRUE, 'light', 'zh-CN'),
(2, TRUE, 'light', 'zh-CN'),
(3, TRUE, 'light', 'zh-CN'),
(4, TRUE, 'light', 'zh-CN'),
(5, TRUE, 'light', 'zh-CN'),
(6, TRUE, 'light', 'zh-CN'),
(7, TRUE, 'light', 'zh-CN'),
(8, TRUE, 'light', 'zh-CN'),
(9, TRUE, 'light', 'zh-CN'),
(10, TRUE, 'light', 'zh-CN');

-- 插入示例课程
INSERT INTO `courses` (`school_id`, `teacher_id`, `student_id`, `classroom_id`, `course_date`, `start_time`, `end_time`, `price`, `grade_level`, `status`) VALUES
(1, 5, 1, 1, '2025-02-01', '09:00:00', '10:30:00', 150.00, '高一', 'scheduled'),
(1, 5, 2, 1, '2025-02-01', '14:00:00', '15:30:00', 150.00, '高一', 'scheduled'),
(1, 6, 4, 2, '2025-02-01', '10:00:00', '11:30:00', 160.00, '高一', 'scheduled'),
(2, 8, 7, 6, '2025-02-01', '15:00:00', '16:30:00', 140.00, '初三', 'scheduled'),
(3, 10, 10, 9, '2025-02-01', '16:00:00', '17:30:00', 180.00, '高三', 'scheduled');

-- 插入示例约课信息
INSERT INTO `bookings` (`school_id`, `student_id`, `teacher_id`, `classroom_id`, `preferred_date`, `preferred_time`, `duration`, `grade_level`, `subject`, `status`, `remarks`) VALUES
(1, 3, 5, 1, '2025-02-02', '09:00:00', 90, '高一', '数学', 'pending', '希望加强数学基础'),
(1, 5, 6, 2, '2025-02-02', '14:00:00', 90, '高一', '英语', 'pending', '准备期末考试'),
(2, 8, 8, 6, '2025-02-02', '10:00:00', 90, '初三', '物理', 'pending', '物理实验辅导');

-- 插入示例消息
INSERT INTO `messages` (`school_id`, `sender_id`, `receiver_id`, `type`, `content`, `status`, `priority`) VALUES
(1, 2, 5, 'notification', '请注意明天的课程安排有调整', 'unread', 'normal'),
(1, 2, 6, 'notification', '下周开始新的教学计划', 'unread', 'normal'),
(2, 3, 8, 'notification', '学生考勤情况需要关注', 'unread', 'urgent');

-- 插入示例工资记录
INSERT INTO `salaries` (`teacher_id`, `period`, `base_amount`, `bonus`, `deductions`, `total_amount`) VALUES
(5, '2025-01-01', 5000.00, 500.00, 100.00, 5400.00),
(6, '2025-01-01', 4800.00, 300.00, 50.00, 5050.00),
(7, '2025-01-01', 5200.00, 400.00, 80.00, 5520.00),
(8, '2025-01-01', 4600.00, 200.00, 60.00, 4740.00),
(9, '2025-01-01', 4900.00, 350.00, 70.00, 5180.00),
(10, '2025-01-01', 5500.00, 600.00, 120.00, 5980.00);

-- 插入示例缴费记录
INSERT INTO `payments` (`student_id`, `amount`, `payment_date`, `school_year`, `created_by`) VALUES
(1, 2000.00, '2025-01-15', '2024-2025', 2),
(3, 1800.00, '2025-01-16', '2024-2025', 2),
(5, 2200.00, '2025-01-17', '2024-2025', 2),
(7, 1900.00, '2025-01-18', '2024-2025', 3),
(9, 2100.00, '2025-01-19', '2024-2025', 3);
