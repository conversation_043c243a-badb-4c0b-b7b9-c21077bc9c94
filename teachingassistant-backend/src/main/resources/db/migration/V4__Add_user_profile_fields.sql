-- 为用户表添加个人信息相关字段
-- 添加性别、入职时间、主教科目、教学经验、个人简介等字段

ALTER TABLE `users` 
ADD COLUMN `gender` ENUM('M', 'F') COMMENT '性别：M-男，F-女' AFTER `email`,
ADD COLUMN `hire_date` DATE COMMENT '入职时间' AFTER `gender`,
ADD COLUMN `subject` VARCHAR(50) COMMENT '主教科目（教师角色使用）' AFTER `hire_date`,
ADD COLUMN `experience` INT UNSIGNED COMMENT '教学经验年数（教师角色使用）' AFTER `subject`,
ADD COLUMN `bio` TEXT COMMENT '个人简介' AFTER `experience`,
ADD COLUMN `last_login_at` TIMESTAMP NULL COMMENT '最后登录时间' AFTER `bio`;

-- 为新字段添加索引
CREATE INDEX `idx_users_hire_date` ON `users` (`hire_date`);
CREATE INDEX `idx_users_subject` ON `users` (`subject`);

-- 更新现有用户的示例数据
UPDATE `users` SET 
    `gender` = 'M',
    `hire_date` = '2023-01-01',
    `subject` = CASE 
        WHEN `role` = 'teacher' AND `real_name` LIKE '%张%' THEN '数学'
        WHEN `role` = 'teacher' AND `real_name` LIKE '%李%' THEN '语文'
        WHEN `role` = 'teacher' AND `real_name` LIKE '%王%' THEN '英语'
        WHEN `role` = 'teacher' AND `real_name` LIKE '%赵%' THEN '物理'
        WHEN `role` = 'teacher' AND `real_name` LIKE '%钱%' THEN '化学'
        ELSE NULL
    END,
    `experience` = CASE 
        WHEN `role` = 'teacher' THEN FLOOR(RAND() * 10) + 1
        ELSE NULL
    END,
    `bio` = CASE 
        WHEN `role` = 'super_admin' THEN '系统管理员，负责整个系统的运维和管理工作。'
        WHEN `role` = 'principal' THEN '学校校长，负责学校的整体管理和教学质量监督。'
        WHEN `role` = 'teacher' THEN '专业教师，致力于为学生提供优质的教学服务。'
        ELSE NULL
    END
WHERE `user_id` > 0;
