-- 为用户表添加principalId字段，用于表示老师隶属于哪个校长
-- 一个老师只能隶属于一个校长，一个校长可以管理多个老师

-- 检查列是否已存在，如果不存在则添加
SET @column_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                      WHERE TABLE_SCHEMA = 'teaching_assistant'
                      AND TABLE_NAME = 'users'
                      AND COLUMN_NAME = 'principal_id');

SET @sql = IF(@column_exists = 0,
              'ALTER TABLE `users` ADD COLUMN `principal_id` BIGINT UNSIGNED NULL COMMENT ''所属校长ID（仅老师角色使用）'' AFTER `school_id`',
              'SELECT ''Column principal_id already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查外键约束是否已存在，如果不存在则添加
SET @fk_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                  WHERE TABLE_SCHEMA = 'teaching_assistant'
                  AND TABLE_NAME = 'users'
                  AND CONSTRAINT_NAME = 'fk_users_principal');

SET @sql = IF(@fk_exists = 0,
              'ALTER TABLE `users` ADD CONSTRAINT `fk_users_principal` FOREIGN KEY (`principal_id`) REFERENCES `users`(`user_id`) ON DELETE SET NULL',
              'SELECT ''Foreign key fk_users_principal already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查索引是否已存在，如果不存在则添加
SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = 'teaching_assistant'
                     AND TABLE_NAME = 'users'
                     AND INDEX_NAME = 'idx_users_principal_id');

SET @sql = IF(@index_exists = 0,
              'CREATE INDEX `idx_users_principal_id` ON `users` (`principal_id`)',
              'SELECT ''Index idx_users_principal_id already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 注意：由于MySQL的限制，不能在有外键约束的列上添加CHECK约束
-- 我们将通过应用程序逻辑来确保只有老师角色才能有principalId

-- 更新现有的老师数据，将他们分配给同学校的校长
-- 这里假设每个学校只有一个校长，如果有多个校长，需要手动调整
-- 使用JOIN语法避免MySQL的限制
UPDATE `users` u1
JOIN (
    SELECT DISTINCT u2.school_id, u2.user_id as principal_id
    FROM `users` u2
    WHERE u2.role = 'principal'
) p ON u1.school_id = p.school_id
SET u1.`principal_id` = p.principal_id
WHERE u1.role = 'teacher' AND u1.principal_id IS NULL;
