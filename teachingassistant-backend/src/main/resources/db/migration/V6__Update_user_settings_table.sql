-- 更新用户设置表，添加新的设置字段
-- 添加课程提醒、约课提醒、侧边栏折叠等字段

ALTER TABLE `user_settings` 
ADD COLUMN `class_reminder` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '课程提醒开关' AFTER `language`,
ADD COLUMN `appointment_reminder` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '约课提醒开关' AFTER `class_reminder`,
ADD COLUMN `sidebar_collapsed` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '侧边栏折叠状态' AFTER `appointment_reminder`;

-- 更新现有数据的默认主题为深色模式
UPDATE `user_settings` SET `theme` = 'dark' WHERE `theme` = 'light';

-- 为新字段设置默认值
UPDATE `user_settings` SET 
    `class_reminder` = TRUE,
    `appointment_reminder` = TRUE,
    `sidebar_collapsed` = FALSE
WHERE `class_reminder` IS NULL OR `appointment_reminder` IS NULL OR `sidebar_collapsed` IS NULL;
