server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: teachingassistant-backend

  # 数据源配置
  datasource:
    url: ************************************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 5000ms

  # 邮件配置
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: ljkswzcscdbvbcae
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
    default-encoding: UTF-8

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null
    serialization:
      write-dates-as-timestamps: false

  # Flyway配置
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.teachingassistant.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    default-executor-type: reuse
    default-statement-timeout: 25000

# JWT配置
jwt:
  secret: teachingAssistantSecretKey2024ForJWTTokenGeneration
  expiration: 86400000  # 24小时，单位毫秒
  refresh-expiration: 604800000  # 7天，单位毫秒

# 日志配置
logging:
  level:
    com.teachingassistant: DEBUG
    org.springframework.security: DEBUG
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/teachingassistant.log
    max-size: 100MB
    max-history: 30

# Kaptcha验证码配置
kaptcha:
  border: yes
  border-color: 105,179,90
  font-color: blue
  image-width: 120
  image-height: 40
  font-size: 30
  char-space: 3
  char-length: 4
  font-names: Arial,Courier
  noise-color: white
  noise-impl: com.google.code.kaptcha.impl.DefaultNoise
  background-color-from: lightGray
  background-color-to: white
  char-string: 0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ

# 应用配置
app:
  name: Teaching Assistant System
  version: 1.0.0
  description: 助教排课系统
  cors:
    allowed-origins: "http://localhost:3000,http://localhost:5173"
    allowed-methods: "GET,POST,PUT,DELETE,OPTIONS"
    allowed-headers: "*"
    allow-credentials: true
