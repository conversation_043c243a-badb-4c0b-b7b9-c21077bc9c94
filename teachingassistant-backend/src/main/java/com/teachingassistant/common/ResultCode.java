package com.teachingassistant.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR> Assistant System
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    // 通用状态码
    SUCCESS(200, "操作成功"),
    ERROR(500, "操作失败"),
    
    // 参数相关
    PARAM_ERROR(400, "参数错误"),
    PARAM_MISSING(400, "缺少必要参数"),
    PARAM_INVALID(400, "参数格式不正确"),
    
    // 认证授权相关
    UNAUTHORIZED(401, "未授权访问"),
    TOKEN_INVALID(401, "Token无效"),
    TOKEN_EXPIRED(401, "Token已过期"),
    LOGIN_FAILED(401, "登录失败"),
    PASSWORD_ERROR(401, "密码错误"),
    ACCOUNT_DISABLED(403, "账户已禁用"),
    PERMISSION_DENIED(403, "权限不足"),
    
    // 资源相关
    NOT_FOUND(404, "资源不存在"),
    RESOURCE_EXISTS(409, "资源已存在"),
    RESOURCE_CONFLICT(409, "资源冲突"),
    DATA_EXISTS(409, "数据已存在"),
    DATA_NOT_FOUND(404, "数据不存在"),
    
    // 业务相关
    USER_NOT_FOUND(1001, "用户不存在"),
    USER_EXISTS(1002, "用户已存在"),
    SCHOOL_NOT_FOUND(1003, "学校不存在"),
    STUDENT_NOT_FOUND(1004, "学生不存在"),
    TEACHER_NOT_FOUND(1005, "老师不存在"),
    CLASSROOM_NOT_FOUND(1006, "教室不存在"),
    COURSE_NOT_FOUND(1007, "课程不存在"),
    CLASS_NOT_FOUND(1008, "班级不存在"),
    
    // 排课相关
    SCHEDULE_CONFLICT(2001, "排课时间冲突"),
    TEACHER_BUSY(2002, "老师时间冲突"),
    CLASSROOM_BUSY(2003, "教室时间冲突"),
    STUDENT_BUSY(2004, "学生时间冲突"),
    SCHEDULE_INVALID(2005, "排课信息无效"),
    
    // 数据库相关
    DATABASE_ERROR(3001, "数据库操作失败"),
    DATA_INTEGRITY_ERROR(3002, "数据完整性错误"),
    
    // 系统相关
    SYSTEM_ERROR(9999, "系统内部错误");
    
    /**
     * 状态码
     */
    private final Integer code;
    
    /**
     * 状态消息
     */
    private final String message;
}
