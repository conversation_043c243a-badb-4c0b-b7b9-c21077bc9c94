package com.teachingassistant;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 助教排课系统主应用类
 * 
 * <AUTHOR> Assistant System
 * @version 1.0.0
 */
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableTransactionManagement
@MapperScan("com.teachingassistant.mapper")
public class TeachingAssistantApplication {

    public static void main(String[] args) {
        SpringApplication.run(TeachingAssistantApplication.class, args);
        System.out.println("=================================");
        System.out.println("助教排课系统启动成功！");
        System.out.println("访问地址: http://localhost:8080/api");
        System.out.println("=================================");
    }
}
