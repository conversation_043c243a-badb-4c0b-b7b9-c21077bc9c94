package com.teachingassistant.security;

import com.teachingassistant.entity.User;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Collections;

/**
 * Spring Security用户主体
 * 
 * <AUTHOR> Assistant System
 */
@Data
@AllArgsConstructor
public class UserPrincipal implements UserDetails {
    
    private User user;
    
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return Collections.singletonList(new SimpleGrantedAuthority("ROLE_" + user.getRole().toUpperCase()));
    }
    
    @Override
    public String getPassword() {
        return user.getPassword();
    }
    
    @Override
    public String getUsername() {
        return user.getUsername();
    }
    
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }
    
    @Override
    public boolean isAccountNonLocked() {
        return "active".equals(user.getStatus());
    }
    
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }
    
    @Override
    public boolean isEnabled() {
        return "active".equals(user.getStatus());
    }
    
    /**
     * 获取用户ID
     */
    public Long getUserId() {
        return user.getUserId();
    }
    
    /**
     * 获取学校ID
     */
    public Long getSchoolId() {
        return user.getSchoolId();
    }
    
    /**
     * 获取用户角色
     */
    public String getRole() {
        return user.getRole();
    }
    
    /**
     * 获取真实姓名
     */
    public String getRealName() {
        return user.getRealName();
    }
}
