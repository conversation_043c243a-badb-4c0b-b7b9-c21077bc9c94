package com.teachingassistant.service;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.entity.Classroom;

import java.util.List;

/**
 * 教室服务接口
 *
 * <AUTHOR> Assistant System
 */
public interface ClassroomService {

    /**
     * 根据教室ID查询教室
     */
    Classroom findById(Long classroomId);

    /**
     * 根据学校ID查询教室列表
     */
    List<Classroom> findBySchoolId(Long schoolId);

    /**
     * 查询所有教室列表
     */
    List<Classroom> findAll();

    /**
     * 分页查询教室列表（管理员端）
     */
    PageResult<Classroom> findWithPagination(Integer page, Integer size, Long schoolId, 
                                           String name, Integer floor, String type, String status);

    /**
     * 分页查询教室列表（校长端）
     */
    PageResult<Classroom> findBySchoolWithPagination(Long schoolId, Integer page, Integer size,
                                                   String name, Integer floor, String type, String status);

    /**
     * 创建教室
     */
    Classroom createClassroom(Classroom classroom);

    /**
     * 更新教室信息
     */
    Classroom updateClassroom(Classroom classroom);

    /**
     * 删除教室
     */
    void deleteClassroom(Long classroomId);

    /**
     * 检查教室名称是否在同一学校内存在
     */
    boolean existsByNameAndSchoolId(String name, Long schoolId);

    /**
     * 检查教室名称是否在同一学校内存在（排除指定ID）
     */
    boolean existsByNameAndSchoolIdExcludeId(String name, Long schoolId, Long classroomId);

    /**
     * 验证用户是否有权限访问指定教室
     */
    boolean hasPermissionToAccess(Long classroomId, Long userSchoolId, String userRole);
}
