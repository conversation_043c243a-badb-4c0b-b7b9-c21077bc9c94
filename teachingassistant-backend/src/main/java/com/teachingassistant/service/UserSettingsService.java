package com.teachingassistant.service;

import com.teachingassistant.entity.UserSettings;

/**
 * 用户设置服务接口
 * 
 * <AUTHOR> Assistant System
 */
public interface UserSettingsService {
    
    /**
     * 根据用户ID获取用户设置
     */
    UserSettings getUserSettings(Long userId);
    
    /**
     * 创建用户设置
     */
    UserSettings createUserSettings(UserSettings userSettings);
    
    /**
     * 更新用户设置
     */
    UserSettings updateUserSettings(Long userId, UserSettings userSettings);
    
    /**
     * 删除用户设置
     */
    void deleteUserSettings(Long userId);
    
    /**
     * 更新主题设置
     */
    void updateTheme(Long userId, String theme);
    
    /**
     * 更新通知设置
     */
    void updateNotificationSettings(Long userId, Boolean notificationEnabled, 
                                    Boolean classReminder, Boolean appointmentReminder);
    
    /**
     * 更新界面设置
     */
    void updateInterfaceSettings(Long userId, String theme, String language, Boolean sidebarCollapsed);
    
    /**
     * 初始化用户默认设置
     */
    UserSettings initDefaultSettings(Long userId);
}
