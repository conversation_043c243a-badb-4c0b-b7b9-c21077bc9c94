package com.teachingassistant.service.impl;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.entity.Classroom;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.mapper.ClassroomMapper;
import com.teachingassistant.service.ClassroomService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 教室服务实现类
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ClassroomServiceImpl implements ClassroomService {
    
    private final ClassroomMapper classroomMapper;
    
    @Override
    public Classroom findById(Long classroomId) {
        if (classroomId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教室ID不能为空");
        }
        Classroom classroom = classroomMapper.findById(classroomId);
        if (classroom == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND, "教室不存在");
        }
        return classroom;
    }
    
    @Override
    public List<Classroom> findBySchoolId(Long schoolId) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        return classroomMapper.findBySchoolId(schoolId);
    }
    
    @Override
    public List<Classroom> findAll() {
        return classroomMapper.findAll();
    }

    @Override
    public PageResult<Classroom> findWithPagination(Integer page, Integer size, Long schoolId, 
                                                   String name, Integer floor, String type, String status) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        Integer offset = (page - 1) * size;
        List<Classroom> classrooms = classroomMapper.findWithPagination(offset, size, schoolId, name, floor, type, status);
        Integer total = classroomMapper.countClassrooms(schoolId, name, floor, type, status);

        return PageResult.of(page, size, total.longValue(), classrooms);
    }

    @Override
    public PageResult<Classroom> findBySchoolWithPagination(Long schoolId, Integer page, Integer size,
                                                           String name, Integer floor, String type, String status) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        Integer offset = (page - 1) * size;
        List<Classroom> classrooms = classroomMapper.findBySchoolWithPagination(offset, size, schoolId, name, floor, type, status);
        Integer total = classroomMapper.countBySchool(schoolId, name, floor, type, status);

        return PageResult.of(page, size, total.longValue(), classrooms);
    }
    
    @Override
    @Transactional
    public Classroom createClassroom(Classroom classroom) {
        validateClassroomForCreate(classroom);
        
        // 检查教室名称是否在同一学校内已存在
        if (existsByNameAndSchoolId(classroom.getName(), classroom.getSchoolId())) {
            throw new BusinessException(ResultCode.DATA_EXISTS, "该学校内教室名称已存在");
        }
        
        // 设置默认状态
        if (!StringUtils.hasText(classroom.getStatus())) {
            classroom.setStatus("available");
        }
        
        // 设置默认容量
        if (classroom.getCapacity() == null) {
            classroom.setCapacity(30);
        }
        
        // 设置默认类型
        if (!StringUtils.hasText(classroom.getType())) {
            classroom.setType("normal");
        }
        
        int result = classroomMapper.insert(classroom);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "教室创建失败");
        }
        
        log.info("教室创建成功: {} - {}", classroom.getSchoolId(), classroom.getName());
        return findById(classroom.getClassroomId());
    }
    
    @Override
    @Transactional
    public Classroom updateClassroom(Classroom classroom) {
        if (classroom.getClassroomId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教室ID不能为空");
        }
        
        // 检查教室是否存在
        Classroom existingClassroom = findById(classroom.getClassroomId());
        
        // 如果更新教室名称，检查是否在同一学校内已存在
        if (StringUtils.hasText(classroom.getName()) && 
            !classroom.getName().equals(existingClassroom.getName())) {
            if (existsByNameAndSchoolIdExcludeId(classroom.getName(), 
                                               existingClassroom.getSchoolId(), 
                                               classroom.getClassroomId())) {
                throw new BusinessException(ResultCode.DATA_EXISTS, "该学校内教室名称已存在");
            }
        }
        
        int result = classroomMapper.update(classroom);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "教室更新失败");
        }
        
        log.info("教室更新成功: {}", classroom.getClassroomId());
        return findById(classroom.getClassroomId());
    }
    
    @Override
    @Transactional
    public void deleteClassroom(Long classroomId) {
        if (classroomId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教室ID不能为空");
        }
        
        // 检查教室是否存在
        findById(classroomId);
        
        // TODO: 检查教室是否有正在进行的课程，如果有则不允许删除
        
        int result = classroomMapper.deleteById(classroomId);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "教室删除失败");
        }
        
        log.info("教室删除成功: {}", classroomId);
    }
    
    @Override
    public boolean existsByNameAndSchoolId(String name, Long schoolId) {
        if (!StringUtils.hasText(name) || schoolId == null) {
            return false;
        }
        return classroomMapper.existsByNameAndSchoolId(name, schoolId);
    }
    
    @Override
    public boolean existsByNameAndSchoolIdExcludeId(String name, Long schoolId, Long classroomId) {
        if (!StringUtils.hasText(name) || schoolId == null || classroomId == null) {
            return false;
        }
        return classroomMapper.existsByNameAndSchoolIdExcludeId(name, schoolId, classroomId);
    }
    
    @Override
    public boolean hasPermissionToAccess(Long classroomId, Long userSchoolId, String userRole) {
        if (classroomId == null) {
            return false;
        }
        
        // 超级管理员可以访问所有教室
        if ("super_admin".equals(userRole)) {
            return true;
        }
        
        // 校长只能访问自己学校的教室
        if ("principal".equals(userRole) && userSchoolId != null) {
            Classroom classroom = findById(classroomId);
            return userSchoolId.equals(classroom.getSchoolId());
        }
        
        return false;
    }
    
    /**
     * 验证教室创建参数
     */
    private void validateClassroomForCreate(Classroom classroom) {
        if (classroom == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教室信息不能为空");
        }
        
        if (classroom.getSchoolId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        
        if (!StringUtils.hasText(classroom.getName())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教室名称不能为空");
        }
        
        if (classroom.getName().length() > 50) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教室名称长度不能超过50个字符");
        }
        
        if (classroom.getFloor() != null && (classroom.getFloor() < 1 || classroom.getFloor() > 50)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "楼层必须在1-50之间");
        }
        
        if (classroom.getCapacity() != null && (classroom.getCapacity() < 1 || classroom.getCapacity() > 500)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "容量必须在1-500之间");
        }
        
        if (StringUtils.hasText(classroom.getType()) && 
            !List.of("normal", "multimedia", "lab").contains(classroom.getType())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教室类型无效");
        }
        
        if (StringUtils.hasText(classroom.getStatus()) && 
            !List.of("available", "in_use", "maintenance").contains(classroom.getStatus())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "教室状态无效");
        }
    }
}
