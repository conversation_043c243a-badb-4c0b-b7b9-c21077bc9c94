package com.teachingassistant.service.impl;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.entity.School;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.mapper.SchoolMapper;
import com.teachingassistant.service.SchoolService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 学校服务实现类
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SchoolServiceImpl implements SchoolService {
    
    private final SchoolMapper schoolMapper;
    
    @Override
    public School findById(Long schoolId) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        School school = schoolMapper.findById(schoolId);
        if (school == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND, "学校不存在");
        }
        return school;
    }
    
    @Override
    public List<School> findAll() {
        return schoolMapper.findAll();
    }
    
    @Override
    public List<School> findByStatus(String status) {
        if (!StringUtils.hasText(status)) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "状态不能为空");
        }
        return schoolMapper.findByStatus(status);
    }

    @Override
    public PageResult<School> findWithPagination(Integer page, Integer size, String name, String status) {
        if (page == null || page < 1) {
            page = 1;
        }
        if (size == null || size < 1) {
            size = 10;
        }

        Integer offset = (page - 1) * size;
        List<School> schools = schoolMapper.findWithPagination(offset, size, name, status);
        Integer total = schoolMapper.countSchools(name, status);

        return PageResult.of(page, size, total.longValue(), schools);
    }
    
    @Override
    @Transactional
    public School createSchool(School school) {
        validateSchoolForCreate(school);
        
        // 检查学校名称是否已存在
        if (existsByName(school.getName())) {
            throw new BusinessException(ResultCode.DATA_EXISTS, "学校名称已存在");
        }
        
        // 设置默认状态
        if (!StringUtils.hasText(school.getStatus())) {
            school.setStatus("active");
        }
        
        // 设置默认管理员配额
        if (school.getAdminQuota() == null) {
            school.setAdminQuota(3);
        }
        
        int result = schoolMapper.insert(school);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "学校创建失败");
        }
        
        log.info("学校创建成功: {}", school.getName());
        return findById(school.getSchoolId());
    }
    
    @Override
    @Transactional
    public School updateSchool(School school) {
        if (school.getSchoolId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        
        // 检查学校是否存在
        School existingSchool = findById(school.getSchoolId());
        
        // 如果更新学校名称，检查是否已存在
        if (StringUtils.hasText(school.getName()) && !school.getName().equals(existingSchool.getName())) {
            if (existsByName(school.getName())) {
                throw new BusinessException(ResultCode.DATA_EXISTS, "学校名称已存在");
            }
        }
        
        int result = schoolMapper.update(school);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "学校更新失败");
        }
        
        log.info("学校更新成功: {}", school.getSchoolId());
        return findById(school.getSchoolId());
    }
    
    @Override
    @Transactional
    public void deleteSchool(Long schoolId) {
        if (schoolId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
        }
        
        // 检查学校是否存在
        findById(schoolId);
        
        // TODO: 检查学校下是否还有用户，如果有则不允许删除
        
        int result = schoolMapper.deleteById(schoolId);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "学校删除失败");
        }
        
        log.info("学校删除成功: {}", schoolId);
    }
    
    @Override
    public boolean existsByName(String name) {
        if (!StringUtils.hasText(name)) {
            return false;
        }
        return schoolMapper.existsByName(name);
    }
    
    /**
     * 验证学校创建参数
     */
    private void validateSchoolForCreate(School school) {
        if (school == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校信息不能为空");
        }
        if (!StringUtils.hasText(school.getName())) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "学校名称不能为空");
        }
        if (school.getAdminQuota() != null && school.getAdminQuota() < 1) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "管理员配额不能小于1");
        }
    }
}
