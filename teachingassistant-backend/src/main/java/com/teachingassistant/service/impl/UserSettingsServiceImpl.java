package com.teachingassistant.service.impl;

import com.teachingassistant.common.ResultCode;
import com.teachingassistant.entity.UserSettings;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.mapper.UserSettingsMapper;
import com.teachingassistant.service.UserService;
import com.teachingassistant.service.UserSettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户设置服务实现类
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserSettingsServiceImpl implements UserSettingsService {
    
    private final UserSettingsMapper userSettingsMapper;
    private final UserService userService;
    
    @Override
    public UserSettings getUserSettings(Long userId) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        
        // 检查用户是否存在
        userService.findById(userId);
        
        UserSettings settings = userSettingsMapper.findByUserId(userId);
        if (settings == null) {
            // 如果用户设置不存在，创建默认设置
            settings = initDefaultSettings(userId);
        }
        
        return settings;
    }
    
    @Override
    @Transactional
    public UserSettings createUserSettings(UserSettings userSettings) {
        if (userSettings == null || userSettings.getUserId() == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户设置信息不能为空");
        }
        
        // 检查用户是否存在
        userService.findById(userSettings.getUserId());
        
        // 检查用户设置是否已存在
        if (userSettingsMapper.existsByUserId(userSettings.getUserId())) {
            throw new BusinessException(ResultCode.DATA_EXISTS, "用户设置已存在");
        }
        
        // 设置默认值
        if (userSettings.getNotificationEnabled() == null) {
            userSettings.setNotificationEnabled(true);
        }
        if (userSettings.getTheme() == null) {
            userSettings.setTheme("dark");
        }
        if (userSettings.getLanguage() == null) {
            userSettings.setLanguage("zh-CN");
        }
        if (userSettings.getClassReminder() == null) {
            userSettings.setClassReminder(true);
        }
        if (userSettings.getAppointmentReminder() == null) {
            userSettings.setAppointmentReminder(true);
        }
        if (userSettings.getSidebarCollapsed() == null) {
            userSettings.setSidebarCollapsed(false);
        }
        
        int result = userSettingsMapper.insert(userSettings);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "用户设置创建失败");
        }
        
        log.info("用户设置创建成功: {}", userSettings.getUserId());
        return userSettings;
    }
    
    @Override
    @Transactional
    public UserSettings updateUserSettings(Long userId, UserSettings userSettings) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        if (userSettings == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户设置信息不能为空");
        }
        
        // 检查用户是否存在
        userService.findById(userId);
        
        // 检查用户设置是否存在
        if (!userSettingsMapper.existsByUserId(userId)) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND, "用户设置不存在");
        }
        
        userSettings.setUserId(userId);
        int result = userSettingsMapper.update(userSettings);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "用户设置更新失败");
        }
        
        log.info("用户设置更新成功: {}", userId);
        return getUserSettings(userId);
    }
    
    @Override
    @Transactional
    public void deleteUserSettings(Long userId) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        
        // 检查用户设置是否存在
        if (!userSettingsMapper.existsByUserId(userId)) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND, "用户设置不存在");
        }
        
        int result = userSettingsMapper.deleteByUserId(userId);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "用户设置删除失败");
        }
        
        log.info("用户设置删除成功: {}", userId);
    }
    
    @Override
    @Transactional
    public void updateTheme(Long userId, String theme) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        if (theme == null || (!theme.equals("light") && !theme.equals("dark"))) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "主题参数无效");
        }
        
        // 检查用户是否存在
        userService.findById(userId);
        
        // 如果用户设置不存在，先创建默认设置
        if (!userSettingsMapper.existsByUserId(userId)) {
            initDefaultSettings(userId);
        }
        
        int result = userSettingsMapper.updateTheme(userId, theme);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "主题设置更新失败");
        }
        
        log.info("用户主题设置更新成功: {} -> {}", userId, theme);
    }
    
    @Override
    @Transactional
    public void updateNotificationSettings(Long userId, Boolean notificationEnabled, 
                                           Boolean classReminder, Boolean appointmentReminder) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        
        // 检查用户是否存在
        userService.findById(userId);
        
        // 如果用户设置不存在，先创建默认设置
        if (!userSettingsMapper.existsByUserId(userId)) {
            initDefaultSettings(userId);
        }
        
        int result = userSettingsMapper.updateNotificationSettings(userId, notificationEnabled, 
                                                                   classReminder, appointmentReminder);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "通知设置更新失败");
        }
        
        log.info("用户通知设置更新成功: {}", userId);
    }
    
    @Override
    @Transactional
    public void updateInterfaceSettings(Long userId, String theme, String language, Boolean sidebarCollapsed) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        
        // 检查用户是否存在
        userService.findById(userId);
        
        // 如果用户设置不存在，先创建默认设置
        if (!userSettingsMapper.existsByUserId(userId)) {
            initDefaultSettings(userId);
        }
        
        int result = userSettingsMapper.updateInterfaceSettings(userId, theme, language, sidebarCollapsed);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "界面设置更新失败");
        }
        
        log.info("用户界面设置更新成功: {}", userId);
    }
    
    @Override
    @Transactional
    public UserSettings initDefaultSettings(Long userId) {
        if (userId == null) {
            throw new BusinessException(ResultCode.PARAM_INVALID, "用户ID不能为空");
        }
        
        // 检查用户是否存在
        userService.findById(userId);
        
        // 如果设置已存在，直接返回
        if (userSettingsMapper.existsByUserId(userId)) {
            return userSettingsMapper.findByUserId(userId);
        }
        
        UserSettings defaultSettings = new UserSettings();
        defaultSettings.setUserId(userId);
        defaultSettings.setNotificationEnabled(true);
        defaultSettings.setTheme("dark");
        defaultSettings.setLanguage("zh-CN");
        defaultSettings.setClassReminder(true);
        defaultSettings.setAppointmentReminder(true);
        defaultSettings.setSidebarCollapsed(false);
        
        int result = userSettingsMapper.insert(defaultSettings);
        if (result <= 0) {
            throw new BusinessException(ResultCode.DATABASE_ERROR, "默认设置初始化失败");
        }
        
        log.info("用户默认设置初始化成功: {}", userId);
        return defaultSettings;
    }
}
