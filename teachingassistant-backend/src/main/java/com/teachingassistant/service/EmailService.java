package com.teachingassistant.service;

/**
 * 邮件服务接口
 * 
 * <AUTHOR> Assistant System
 */
public interface EmailService {
    
    /**
     * 发送邮箱验证码
     * 
     * @param email 邮箱地址
     * @param code 验证码
     */
    void sendVerificationCode(String email, String code);
    
    /**
     * 生成验证码
     * 
     * @return 6位数字验证码
     */
    String generateVerificationCode();
    
    /**
     * 存储验证码
     * 
     * @param email 邮箱地址
     * @param code 验证码
     */
    void storeVerificationCode(String email, String code);
    
    /**
     * 验证验证码
     * 
     * @param email 邮箱地址
     * @param code 验证码
     * @return 验证是否成功
     */
    boolean verifyCode(String email, String code);
    
    /**
     * 删除验证码
     * 
     * @param email 邮箱地址
     */
    void removeVerificationCode(String email);
}
