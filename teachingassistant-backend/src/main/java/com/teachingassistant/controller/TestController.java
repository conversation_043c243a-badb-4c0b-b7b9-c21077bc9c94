package com.teachingassistant.controller;

import com.teachingassistant.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 测试控制器
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<Map<String, Object>> health() {
        Map<String, Object> data = new HashMap<>();
        data.put("status", "UP");
        data.put("timestamp", LocalDateTime.now());
        data.put("application", "Teaching Assistant System");
        data.put("version", "1.0.0");
        
        log.info("健康检查请求");
        return Result.success("系统运行正常", data);
    }
    
    /**
     * 公开接口测试
     */
    @GetMapping("/public")
    public Result<String> publicEndpoint() {
        return Result.success("这是一个公开接口，无需认证");
    }
    
    /**
     * 需要认证的接口测试
     */
    @GetMapping("/protected")
    public Result<String> protectedEndpoint() {
        return Result.success("这是一个受保护的接口，需要认证");
    }
}
