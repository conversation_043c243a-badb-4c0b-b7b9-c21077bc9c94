package com.teachingassistant.controller;

import com.teachingassistant.common.Result;
import com.teachingassistant.service.CaptchaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 验证码控制器
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@RestController
@RequestMapping("/captcha")
@RequiredArgsConstructor
public class CaptchaController {
    
    private final CaptchaService captchaService;
    
    /**
     * 生成验证码
     * 
     * @return 验证码图片和key
     */
    @GetMapping("/generate")
    public Result<Map<String, String>> generateCaptcha() {
        try {
            log.info("收到生成验证码请求");
            
            CaptchaService.CaptchaResult captchaResult = captchaService.generateCaptcha();
            
            Map<String, String> result = new HashMap<>();
            result.put("captchaKey", captchaResult.getCaptchaKey());
            result.put("captchaImage", "data:image/png;base64," + captchaResult.getImageBase64());
            
            log.info("验证码生成成功，key: {}", captchaResult.getCaptchaKey());
            
            return Result.success("验证码生成成功", result);
            
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            return Result.error("生成验证码失败");
        }
    }
}
