package com.teachingassistant.controller;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.Result;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.dto.CreateUserRequest;
import com.teachingassistant.dto.ResetPasswordRequest;
import com.teachingassistant.dto.UpdateUserRequest;
import com.teachingassistant.dto.UpdateTeacherRequest;
import com.teachingassistant.dto.UserWithSchoolDTO;
import com.teachingassistant.entity.School;
import com.teachingassistant.entity.User;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.service.SchoolService;
import com.teachingassistant.service.UserService;
import com.teachingassistant.util.SubjectUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 管理员控制器
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@RestController
@RequestMapping("/admin")
@RequiredArgsConstructor
public class AdminController {

    private final UserService userService;
    private final SchoolService schoolService;

    /**
     * 分页查询用户列表
     */
    @GetMapping("/users")
    public Result<PageResult<User>> getUserList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long schoolId,
            @RequestParam(required = false) String role,
            @RequestParam(required = false) String realName) {
        try {
            PageResult<User> result = userService.findWithPagination(page, size, schoolId, role, realName);
            log.info("管理员查询用户列表成功，页码: {}, 大小: {}, 总数: {}", page, size, result.getTotal());
            return Result.success(result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询用户列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询用户列表失败");
        }
    }

    /**
     * 根据ID查询用户详情
     */
    @GetMapping("/users/{userId}")
    public Result<User> getUserById(@PathVariable Long userId) {
        try {
            User user = userService.findById(userId);
            log.info("管理员查询用户详情成功: {}", userId);
            return Result.success(user);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询用户详情失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询用户详情失败");
        }
    }

    /**
     * 创建用户
     */
    @PostMapping("/users")
    public Result<User> createUser(@Valid @RequestBody CreateUserRequest request) {
        try {
            User user = new User();
            user.setUsername(request.getUsername());
            user.setPassword(request.getPassword());
            user.setRealName(request.getRealName());
            user.setRole(request.getRole());
            user.setSchoolId(request.getSchoolId());
            user.setPhone(request.getPhone());
            user.setEmail(request.getEmail());
            user.setGender(request.getGender());
            user.setHireDate(request.getHireDate());
            user.setSubject(request.getSubject());
            user.setExperience(request.getExperience());
            user.setBio(request.getBio());

            User createdUser = userService.createUser(user);
            log.info("管理员创建用户成功: {}", createdUser.getUsername());
            return Result.success(createdUser);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建用户失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "创建用户失败");
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/users/{userId}")
    public Result<User> updateUser(@PathVariable Long userId, @Valid @RequestBody UpdateUserRequest request) {
        try {
            User user = new User();
            user.setUserId(userId);
            user.setRealName(request.getRealName());
            user.setPhone(request.getPhone());
            user.setEmail(request.getEmail());
            user.setGender(request.getGender());
            user.setSchoolId(request.getSchoolId());
            user.setHireDate(request.getHireDate());
            user.setSubject(request.getSubject());
            user.setExperience(request.getExperience());
            user.setBio(request.getBio());
            user.setStatus(request.getStatus());

            User updatedUser = userService.updateUser(user);
            log.info("管理员更新用户信息成功: {}", userId);
            return Result.success(updatedUser);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户信息失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "更新用户信息失败");
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/users/{userId}")
    public Result<Void> deleteUser(@PathVariable Long userId) {
        try {
            userService.deleteUser(userId);
            log.info("管理员删除用户成功: {}", userId);
            return Result.success("用户删除成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除用户失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "删除用户失败");
        }
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/users/{userId}/password")
    public Result<Void> resetUserPassword(@PathVariable Long userId, @Valid @RequestBody ResetPasswordRequest request) {
        try {
            // 检查用户是否存在
            User user = userService.findById(userId);
            
            // 重置密码
            userService.resetPassword(userId, request.getNewPassword());
            
            log.info("管理员重置用户密码成功: {}", userId);
            return Result.success("密码重置成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("重置用户密码失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "重置用户密码失败");
        }
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/users/{userId}/status")
    public Result<Void> updateUserStatus(@PathVariable Long userId, @RequestParam String status) {
        try {
            userService.updateStatus(userId, status);
            log.info("管理员更新用户状态成功: {} -> {}", userId, status);
            return Result.success("用户状态更新成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户状态失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "更新用户状态失败");
        }
    }

    /**
     * 获取所有学校列表（用于下拉选择）
     */
    @GetMapping("/schools/all")
    public Result<List<School>> getAllSchools() {
        try {
            List<School> schools = schoolService.findAll();
            log.info("管理员获取学校列表成功，数量: {}", schools.size());
            return Result.success(schools);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取学校列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取学校列表失败");
        }
    }

    /**
     * 分页查询校长列表
     */
    @GetMapping("/principals")
    public Result<PageResult<UserWithSchoolDTO>> getPrincipalList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long schoolId,
            @RequestParam(required = false) String realName) {
        try {
            PageResult<User> userResult = userService.findWithPagination(page, size, schoolId, "principal", realName);

            // 转换为DTO
            List<UserWithSchoolDTO> dtoList = userResult.getRecords().stream()
                    .map(UserWithSchoolDTO::fromUser)
                    .collect(Collectors.toList());

            PageResult<UserWithSchoolDTO> result = PageResult.of(
                    userResult.getPage(),
                    userResult.getSize(),
                    userResult.getTotal(),
                    dtoList
            );

            log.info("管理员查询校长列表成功，页码: {}, 大小: {}, 总数: {}", page, size, result.getTotal());
            return Result.success(result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询校长列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询校长列表失败");
        }
    }

    /**
     * 创建校长账号
     */
    @PostMapping("/principals")
    public Result<UserWithSchoolDTO> createPrincipal(@Valid @RequestBody CreateUserRequest request) {
        try {
            // 强制设置角色为校长，覆盖请求中的角色
            request.setRole("principal");

            User user = new User();
            user.setUsername(request.getUsername());
            user.setPassword(request.getPassword());
            user.setRealName(request.getRealName());
            user.setRole("principal"); // 强制设置角色为校长
            user.setSchoolId(request.getSchoolId());
            user.setPhone(request.getPhone());
            user.setEmail(request.getEmail());
            user.setGender(request.getGender());
            user.setHireDate(request.getHireDate());
            user.setSubject(request.getSubject());
            user.setExperience(request.getExperience());
            user.setBio(request.getBio());

            User createdUser = userService.createUser(user);
            log.info("管理员创建校长成功: {}", createdUser.getUsername());
            return Result.success(UserWithSchoolDTO.fromUser(createdUser));
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建校长失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "创建校长失败");
        }
    }

    /**
     * 更新校长信息
     */
    @PutMapping("/principals/{userId}")
    public Result<UserWithSchoolDTO> updatePrincipal(@PathVariable Long userId,
                                       @Valid @RequestBody UpdateUserRequest request) {
        try {
            User user = new User();
            user.setUserId(userId);
            user.setRealName(request.getRealName());
            user.setPhone(request.getPhone());
            user.setEmail(request.getEmail());
            user.setGender(request.getGender());
            user.setSchoolId(request.getSchoolId());
            user.setHireDate(request.getHireDate());
            user.setSubject(request.getSubject());
            user.setExperience(request.getExperience());
            user.setBio(request.getBio());
            user.setStatus(request.getStatus());

            User updatedUser = userService.updateUser(user);
            log.info("管理员更新校长信息成功: {}", userId);
            return Result.success(UserWithSchoolDTO.fromUser(updatedUser));
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新校长信息失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "更新校长信息失败");
        }
    }

    /**
     * 删除校长账号
     */
    @DeleteMapping("/principals/{userId}")
    public Result<Void> deletePrincipal(@PathVariable Long userId) {
        try {
            // 验证用户是否为校长角色
            User user = userService.findById(userId);
            if (!"principal".equals(user.getRole())) {
                throw new BusinessException(ResultCode.PARAM_INVALID, "该用户不是校长角色");
            }

            userService.deleteUser(userId);
            log.info("管理员删除校长成功: {}", userId);
            return Result.success("校长删除成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除校长失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "删除校长失败");
        }
    }

    /**
     * 分页查询老师列表（包含校长信息）
     */
    @GetMapping("/teachers")
    public Result<PageResult<User>> getTeacherList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long schoolId,
            @RequestParam(required = false) Long principalId,
            @RequestParam(required = false) String realName) {
        try {
            PageResult<User> result = userService.findTeachersWithPrincipal(page, size, schoolId, principalId, realName);
            log.info("管理员查询老师列表成功，页码: {}, 大小: {}, 总数: {}", page, size, result.getTotal());
            return Result.success(result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询老师列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询老师列表失败");
        }
    }

    /**
     * 根据ID获取老师详细信息
     */
    @GetMapping("/teachers/{teacherId}")
    public Result<User> getTeacherById(@PathVariable Long teacherId) {
        try {
            User teacher = userService.findById(teacherId);
            if (!"teacher".equals(teacher.getRole())) {
                throw new BusinessException(ResultCode.PARAM_INVALID, "该用户不是老师角色");
            }
            log.info("管理员获取老师详细信息成功: {}", teacherId);
            return Result.success(teacher);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取老师详细信息失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "获取老师详细信息失败");
        }
    }

    /**
     * 更新老师信息
     */
    @PutMapping("/teachers/{teacherId}")
    public Result<User> updateTeacher(@PathVariable Long teacherId, @Valid @RequestBody UpdateTeacherRequest request) {
        try {
            // 验证老师是否存在
            User existingTeacher = userService.findById(teacherId);
            if (!"teacher".equals(existingTeacher.getRole())) {
                throw new BusinessException(ResultCode.PARAM_INVALID, "该用户不是老师角色");
            }

            // 验证科目数据
            if (request.getSubject() != null && !request.getSubject().isEmpty()) {
                if (!SubjectUtils.areValidSubjects(request.getSubject())) {
                    throw new BusinessException(ResultCode.PARAM_INVALID, "包含无效的科目");
                }
            }

            User user = new User();
            user.setUserId(teacherId);
            user.setRealName(request.getRealName());
            user.setPhone(request.getPhone());
            user.setEmail(request.getEmail());
            user.setGender(request.getGender());
            // 将科目数组转换为逗号分隔的字符串
            user.setSubject(SubjectUtils.arrayToString(request.getSubject()));
            user.setExperience(request.getExperience());
            user.setBio(request.getBio());
            user.setStatus(request.getStatus());

            User updatedTeacher = userService.updateUser(user);
            log.info("管理员更新老师信息成功: {}", teacherId);
            return Result.success(updatedTeacher);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新老师信息失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "更新老师信息失败");
        }
    }

    /**
     * 修改老师与校长的关系
     */
    @PutMapping("/teachers/{teacherId}/principal")
    public Result<Void> updateTeacherPrincipal(@PathVariable Long teacherId,
                                               @RequestParam(required = false) Long principalId) {
        try {
            // 验证老师是否存在
            User teacher = userService.findById(teacherId);
            if (!"teacher".equals(teacher.getRole())) {
                throw new BusinessException(ResultCode.PARAM_INVALID, "该用户不是老师角色");
            }

            // 如果指定了校长ID，验证校长是否存在
            if (principalId != null) {
                User principal = userService.findById(principalId);
                if (!"principal".equals(principal.getRole())) {
                    throw new BusinessException(ResultCode.PARAM_INVALID, "指定的用户不是校长角色");
                }

                // 验证校长和老师是否在同一学校
                if (!principal.getSchoolId().equals(teacher.getSchoolId())) {
                    throw new BusinessException(ResultCode.PARAM_INVALID, "校长和老师必须在同一学校");
                }
            }

            // 更新老师的校长ID
            User updateUser = new User();
            updateUser.setUserId(teacherId);
            updateUser.setPrincipalId(principalId);
            userService.updateUser(updateUser);

            log.info("管理员修改老师校长关系成功: {} -> {}", teacherId, principalId);
            return Result.success("老师校长关系修改成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("修改老师校长关系失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "修改老师校长关系失败");
        }
    }
}
