package com.teachingassistant.controller;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.Result;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.dto.CreateClassroomRequest;
import com.teachingassistant.dto.UpdateClassroomRequest;
import com.teachingassistant.entity.Classroom;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.security.UserPrincipal;
import com.teachingassistant.service.ClassroomService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 教室管理控制器
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class ClassroomController {
    
    private final ClassroomService classroomService;
    
    /**
     * 获取教室列表（管理员端 - 分页）
     */
    @GetMapping("/admin/classrooms")
    public Result<PageResult<Classroom>> getClassroomList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) Long schoolId,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer floor,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status) {
        try {
            PageResult<Classroom> result = classroomService.findWithPagination(
                page, size, schoolId, name, floor, type, status);
            return Result.success("获取教室列表成功", result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取教室列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 获取教室列表（校长端 - 分页）
     */
    @GetMapping("/principal/classrooms")
    public Result<PageResult<Classroom>> getPrincipalClassroomList(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer floor,
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String status) {
        try {
            Long schoolId = userPrincipal.getSchoolId();
            if (schoolId == null) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "用户未关联学校");
            }
            
            PageResult<Classroom> result = classroomService.findBySchoolWithPagination(
                schoolId, page, size, name, floor, type, status);
            return Result.success("获取教室列表成功", result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取教室列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 获取所有教室列表（管理员端 - 不分页）
     */
    @GetMapping("/admin/classrooms/all")
    public Result<List<Classroom>> getAllClassrooms() {
        try {
            List<Classroom> classrooms = classroomService.findAll();
            return Result.success("获取教室列表成功", classrooms);
        } catch (Exception e) {
            log.error("获取教室列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 获取学校教室列表（校长端 - 不分页）
     */
    @GetMapping("/principal/classrooms/all")
    public Result<List<Classroom>> getPrincipalAllClassrooms(
            @AuthenticationPrincipal UserPrincipal userPrincipal) {
        try {
            Long schoolId = userPrincipal.getSchoolId();
            if (schoolId == null) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "用户未关联学校");
            }
            
            List<Classroom> classrooms = classroomService.findBySchoolId(schoolId);
            return Result.success("获取教室列表成功", classrooms);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取教室列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 根据ID获取教室详情（管理员端）
     */
    @GetMapping("/admin/classrooms/{classroomId}")
    public Result<Classroom> getClassroomById(@PathVariable Long classroomId) {
        try {
            Classroom classroom = classroomService.findById(classroomId);
            return Result.success("获取教室详情成功", classroom);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取教室详情失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 根据ID获取教室详情（校长端）
     */
    @GetMapping("/principal/classrooms/{classroomId}")
    public Result<Classroom> getPrincipalClassroomById(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long classroomId) {
        try {
            // 验证权限
            if (!classroomService.hasPermissionToAccess(classroomId, 
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权访问该教室");
            }
            
            Classroom classroom = classroomService.findById(classroomId);
            return Result.success("获取教室详情成功", classroom);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取教室详情失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 创建教室（管理员端）
     */
    @PostMapping("/admin/classrooms")
    public Result<Classroom> createClassroom(@Valid @RequestBody CreateClassroomRequest request) {
        try {
            if (request.getSchoolId() == null) {
                throw new BusinessException(ResultCode.PARAM_INVALID, "学校ID不能为空");
            }
            
            Classroom classroom = new Classroom();
            BeanUtils.copyProperties(request, classroom);
            
            Classroom createdClassroom = classroomService.createClassroom(classroom);
            return Result.success("教室创建成功", createdClassroom);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建教室失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 创建教室（校长端）
     */
    @PostMapping("/principal/classrooms")
    public Result<Classroom> createPrincipalClassroom(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @Valid @RequestBody CreateClassroomRequest request) {
        try {
            Long schoolId = userPrincipal.getSchoolId();
            if (schoolId == null) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "用户未关联学校");
            }
            
            Classroom classroom = new Classroom();
            BeanUtils.copyProperties(request, classroom);
            classroom.setSchoolId(schoolId); // 强制设置为当前用户的学校ID
            
            Classroom createdClassroom = classroomService.createClassroom(classroom);
            return Result.success("教室创建成功", createdClassroom);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建教室失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 更新教室信息（管理员端）
     */
    @PutMapping("/admin/classrooms/{classroomId}")
    public Result<Classroom> updateClassroom(
            @PathVariable Long classroomId,
            @Valid @RequestBody UpdateClassroomRequest request) {
        try {
            Classroom classroom = new Classroom();
            BeanUtils.copyProperties(request, classroom);
            classroom.setClassroomId(classroomId);
            
            Classroom updatedClassroom = classroomService.updateClassroom(classroom);
            return Result.success("教室更新成功", updatedClassroom);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新教室失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 更新教室信息（校长端）
     */
    @PutMapping("/principal/classrooms/{classroomId}")
    public Result<Classroom> updatePrincipalClassroom(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long classroomId,
            @Valid @RequestBody UpdateClassroomRequest request) {
        try {
            // 验证权限
            if (!classroomService.hasPermissionToAccess(classroomId, 
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权修改该教室");
            }
            
            Classroom classroom = new Classroom();
            BeanUtils.copyProperties(request, classroom);
            classroom.setClassroomId(classroomId);
            
            Classroom updatedClassroom = classroomService.updateClassroom(classroom);
            return Result.success("教室更新成功", updatedClassroom);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新教室失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 删除教室（管理员端）
     */
    @DeleteMapping("/admin/classrooms/{classroomId}")
    public Result<Void> deleteClassroom(@PathVariable Long classroomId) {
        try {
            classroomService.deleteClassroom(classroomId);
            return Result.success("教室删除成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除教室失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
    
    /**
     * 删除教室（校长端）
     */
    @DeleteMapping("/principal/classrooms/{classroomId}")
    public Result<Void> deletePrincipalClassroom(
            @AuthenticationPrincipal UserPrincipal userPrincipal,
            @PathVariable Long classroomId) {
        try {
            // 验证权限
            if (!classroomService.hasPermissionToAccess(classroomId, 
                    userPrincipal.getSchoolId(), userPrincipal.getRole())) {
                throw new BusinessException(ResultCode.PERMISSION_DENIED, "无权删除该教室");
            }
            
            classroomService.deleteClassroom(classroomId);
            return Result.success("教室删除成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除教室失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR);
        }
    }
}
