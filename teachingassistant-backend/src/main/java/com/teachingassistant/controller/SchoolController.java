package com.teachingassistant.controller;

import com.teachingassistant.common.PageResult;
import com.teachingassistant.common.Result;
import com.teachingassistant.common.ResultCode;
import com.teachingassistant.dto.CreateSchoolRequest;
import com.teachingassistant.dto.UpdateSchoolRequest;
import com.teachingassistant.entity.School;
import com.teachingassistant.exception.BusinessException;
import com.teachingassistant.service.SchoolService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 学校管理控制器
 * 
 * <AUTHOR> Assistant System
 */
@Slf4j
@RestController
@RequestMapping("/admin/schools")
@RequiredArgsConstructor
public class SchoolController {

    private final SchoolService schoolService;

    /**
     * 分页查询学校列表
     */
    @GetMapping
    public Result<PageResult<School>> getSchoolList(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String status) {
        try {
            PageResult<School> result = schoolService.findWithPagination(page, size, name, status);
            log.info("查询学校列表成功，页码: {}, 大小: {}, 总数: {}", page, size, result.getTotal());
            return Result.success(result);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询学校列表失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询学校列表失败");
        }
    }

    /**
     * 根据ID查询学校详情
     */
    @GetMapping("/{schoolId}")
    public Result<School> getSchoolById(@PathVariable Long schoolId) {
        try {
            School school = schoolService.findById(schoolId);
            log.info("查询学校详情成功: {}", schoolId);
            return Result.success(school);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("查询学校详情失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "查询学校详情失败");
        }
    }

    /**
     * 创建学校
     */
    @PostMapping
    public Result<School> createSchool(@Valid @RequestBody CreateSchoolRequest request) {
        try {
            School school = new School();
            school.setName(request.getName());
            school.setAddress(request.getAddress());
            school.setPhone(request.getPhone());
            school.setEmail(request.getEmail());
            school.setAdminQuota(request.getAdminQuota());
            school.setStatus(request.getStatus());

            School createdSchool = schoolService.createSchool(school);
            log.info("创建学校成功: {}", createdSchool.getName());
            return Result.success(createdSchool);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建学校失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "创建学校失败");
        }
    }

    /**
     * 更新学校信息
     */
    @PutMapping("/{schoolId}")
    public Result<School> updateSchool(@PathVariable Long schoolId, 
                                      @Valid @RequestBody UpdateSchoolRequest request) {
        try {
            School school = new School();
            school.setSchoolId(schoolId);
            school.setName(request.getName());
            school.setAddress(request.getAddress());
            school.setPhone(request.getPhone());
            school.setEmail(request.getEmail());
            school.setAdminQuota(request.getAdminQuota());
            school.setStatus(request.getStatus());

            School updatedSchool = schoolService.updateSchool(school);
            log.info("更新学校信息成功: {}", schoolId);
            return Result.success(updatedSchool);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新学校信息失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "更新学校信息失败");
        }
    }

    /**
     * 删除学校
     */
    @DeleteMapping("/{schoolId}")
    public Result<Void> deleteSchool(@PathVariable Long schoolId) {
        try {
            schoolService.deleteSchool(schoolId);
            log.info("删除学校成功: {}", schoolId);
            return Result.success("学校删除成功");
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("删除学校失败", e);
            throw new BusinessException(ResultCode.SYSTEM_ERROR, "删除学校失败");
        }
    }
}
