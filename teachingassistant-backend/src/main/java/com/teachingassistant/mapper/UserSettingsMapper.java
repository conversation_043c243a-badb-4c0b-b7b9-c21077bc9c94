package com.teachingassistant.mapper;

import com.teachingassistant.entity.UserSettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户设置数据访问层
 * 
 * <AUTHOR> Assistant System
 */
@Mapper
public interface UserSettingsMapper {
    
    /**
     * 根据用户ID查询用户设置
     */
    UserSettings findByUserId(@Param("userId") Long userId);
    
    /**
     * 插入用户设置
     */
    int insert(UserSettings userSettings);
    
    /**
     * 更新用户设置
     */
    int update(UserSettings userSettings);
    
    /**
     * 根据用户ID删除设置
     */
    int deleteByUserId(@Param("userId") Long userId);
    
    /**
     * 检查用户设置是否存在
     */
    boolean existsByUserId(@Param("userId") Long userId);
    
    /**
     * 更新主题设置
     */
    int updateTheme(@Param("userId") Long userId, @Param("theme") String theme);
    
    /**
     * 更新通知设置
     */
    int updateNotificationSettings(@Param("userId") Long userId, 
                                   @Param("notificationEnabled") Boolean notificationEnabled,
                                   @Param("classReminder") Boolean classReminder,
                                   @Param("appointmentReminder") Boolean appointmentReminder);
    
    /**
     * 更新界面设置
     */
    int updateInterfaceSettings(@Param("userId") Long userId,
                                @Param("theme") String theme,
                                @Param("language") String language,
                                @Param("sidebarCollapsed") Boolean sidebarCollapsed);
}
