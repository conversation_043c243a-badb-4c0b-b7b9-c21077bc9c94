package com.teachingassistant.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 教室实体类
 * 
 * <AUTHOR> Assistant System
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Classroom extends BaseEntity {
    
    /**
     * 教室ID
     */
    private Long classroomId;
    
    /**
     * 所属学校ID
     */
    private Long schoolId;
    
    /**
     * 教室名称
     */
    private String name;
    
    /**
     * 教室状态：available-可用，in_use-使用中，maintenance-维护中
     */
    private String status;
    
    /**
     * 学校信息（关联查询时使用）
     */
    private School school;
    
    /**
     * 当前课程信息（状态查询时使用）
     */
    private Course currentCourse;
    
    /**
     * 使用率（统计字段）
     */
    private Double utilizationRate;
}
