package com.teachingassistant.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.teachingassistant.util.SubjectUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 用户实体类
 * 
 * <AUTHOR> Assistant System
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class User extends BaseEntity {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 所属学校ID
     */
    private Long schoolId;

    /**
     * 所属校长ID（仅老师角色使用）
     */
    private Long principalId;
    
    /**
     * 登录账号
     */
    private String username;
    
    /**
     * 加密密码
     */
    @JsonIgnore
    private String password;
    
    /**
     * 用户角色：super_admin-超级管理员，principal-校长，teacher-老师
     */
    private String role;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 绑定手机
     */
    private String phone;

    /**
     * 邮箱地址
     */
    private String email;

    /**
     * 性别：M-男，F-女
     */
    private String gender;

    /**
     * 入职时间
     */
    private String hireDate;

    /**
     * 主教科目（教师角色使用）
     */
    private String subject;

    /**
     * 教学经验年数（教师角色使用）
     */
    private Integer experience;

    /**
     * 个人简介
     */
    private String bio;

    /**
     * 最后登录时间
     */
    private String lastLoginAt;

    /**
     * MFA设备密钥
     */
    @JsonIgnore
    private String mfaSecret;

    /**
     * 用户状态：active-启用，inactive-停用
     */
    private String status;
    
    /**
     * 学校信息（关联查询时使用）
     */
    private School school;

    /**
     * 所属校长信息（关联查询时使用，仅老师角色）
     */
    private User principal;

    /**
     * 获取学校名称（便捷方法）
     */
    @JsonIgnore
    public String getSchoolName() {
        return school != null ? school.getName() : null;
    }

    /**
     * 获取校长姓名（便捷方法）
     */
    @JsonIgnore
    public String getPrincipalName() {
        return principal != null ? principal.getRealName() : null;
    }

    /**
     * 获取科目数组（用于前端显示）
     */
    @JsonProperty("subjectArray")
    public List<String> getSubjectArray() {
        return SubjectUtils.stringToArray(this.subject);
    }
}
