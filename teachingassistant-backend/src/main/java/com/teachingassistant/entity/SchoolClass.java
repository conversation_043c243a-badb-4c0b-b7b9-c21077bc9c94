package com.teachingassistant.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 班级实体类
 * 
 * <AUTHOR> Assistant System
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SchoolClass extends BaseEntity {
    
    /**
     * 班级ID
     */
    private Long classId;
    
    /**
     * 所属学校ID
     */
    private Long schoolId;
    
    /**
     * 班级名称
     */
    private String name;
    
    /**
     * 班主任ID
     */
    private Long mainTeacher;
    
    /**
     * 班主任信息（关联查询时使用）
     */
    private User mainTeacherInfo;
    
    /**
     * 学校信息（关联查询时使用）
     */
    private School school;
    
    /**
     * 学生数量（统计字段）
     */
    private Integer studentCount;
    
    /**
     * 老师数量（统计字段）
     */
    private Integer teacherCount;
}
