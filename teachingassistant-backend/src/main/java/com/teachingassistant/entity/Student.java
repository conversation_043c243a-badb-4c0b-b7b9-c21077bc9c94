package com.teachingassistant.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 学生实体类
 * 
 * <AUTHOR> Assistant System
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class Student extends BaseEntity {
    
    /**
     * 学生ID
     */
    private Long studentId;
    
    /**
     * 所属学校ID
     */
    private Long schoolId;
    
    /**
     * 学生姓名
     */
    private String name;
    
    /**
     * 班级ID
     */
    private Long classId;
    
    /**
     * 联系电话
     */
    private String contactPhone;
    
    /**
     * 未付金额
     */
    private BigDecimal unpaidAmount;
    
    /**
     * 班级信息（关联查询时使用）
     */
    private SchoolClass schoolClass;
    
    /**
     * 学校信息（关联查询时使用）
     */
    private School school;
}
