package com.teachingassistant.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 学校实体类
 * 
 * <AUTHOR> Assistant System
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class School extends BaseEntity {
    
    /**
     * 学校ID
     */
    private Long schoolId;
    
    /**
     * 学校名称
     */
    private String name;
    
    /**
     * 学校地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 管理员配额
     */
    private Integer adminQuota;

    /**
     * 学校状态：active-启用，inactive-停用
     */
    private String status;
}
