package com.teachingassistant.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户设置实体类
 * 
 * <AUTHOR> Assistant System
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserSettings extends BaseEntity {
    
    /**
     * 设置ID
     */
    private Long settingId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 消息提醒开关
     */
    private Boolean notificationEnabled;
    
    /**
     * 界面主题：light-浅色模式，dark-深色模式
     */
    private String theme;
    
    /**
     * 语言设置
     */
    private String language;
    
    /**
     * 课程提醒开关
     */
    private Boolean classReminder;
    
    /**
     * 约课提醒开关
     */
    private Boolean appointmentReminder;
    
    /**
     * 侧边栏折叠状态
     */
    private Boolean sidebarCollapsed;
    
    /**
     * 用户信息（关联查询时使用）
     */
    private User user;
}
