package com.teachingassistant.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.teachingassistant.entity.User;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息DTO（包含学校名称）
 * 
 * <AUTHOR> Assistant System
 */
@Data
public class UserWithSchoolDTO {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 登录账号
     */
    private String username;
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 用户角色
     */
    private String role;
    
    /**
     * 所属学校ID
     */
    private Long schoolId;
    
    /**
     * 学校名称
     */
    private String schoolName;
    
    /**
     * 绑定手机
     */
    private String phone;
    
    /**
     * 邮箱地址
     */
    private String email;
    
    /**
     * 性别
     */
    private String gender;
    
    /**
     * 入职时间
     */
    private String hireDate;
    
    /**
     * 主教科目
     */
    private String subject;
    
    /**
     * 教学经验年数
     */
    private Integer experience;
    
    /**
     * 个人简介
     */
    private String bio;
    
    /**
     * 最后登录时间
     */
    private String lastLoginAt;
    
    /**
     * 用户状态
     */
    private String status;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    /**
     * 从User实体转换为DTO
     */
    public static UserWithSchoolDTO fromUser(User user) {
        UserWithSchoolDTO dto = new UserWithSchoolDTO();
        dto.setUserId(user.getUserId());
        dto.setUsername(user.getUsername());
        dto.setRealName(user.getRealName());
        dto.setRole(user.getRole());
        dto.setSchoolId(user.getSchoolId());
        dto.setSchoolName(user.getSchoolName());
        dto.setPhone(user.getPhone());
        dto.setEmail(user.getEmail());
        dto.setGender(user.getGender());
        dto.setHireDate(user.getHireDate());
        dto.setSubject(user.getSubject());
        dto.setExperience(user.getExperience());
        dto.setBio(user.getBio());
        dto.setLastLoginAt(user.getLastLoginAt());
        dto.setStatus(user.getStatus());
        dto.setCreatedAt(user.getCreatedAt());
        dto.setUpdatedAt(user.getUpdatedAt());
        return dto;
    }
}
