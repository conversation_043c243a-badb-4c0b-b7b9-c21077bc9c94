package com.teachingassistant.dto;

import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Pattern;

/**
 * 更新学校请求DTO
 * 
 * <AUTHOR> Assistant System
 */
@Data
public class UpdateSchoolRequest {
    
    /**
     * 学校名称
     */
    private String name;
    
    /**
     * 学校地址
     */
    private String address;
    
    /**
     * 联系电话
     */
    private String phone;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 管理员配额
     */
    @Min(value = 1, message = "管理员配额不能小于1")
    private Integer adminQuota;
    
    /**
     * 学校状态：active-启用，inactive-停用
     */
    @Pattern(regexp = "^(active|inactive)$", message = "状态只能是active或inactive")
    private String status;
}
