package com.teachingassistant.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 更新主题请求DTO
 * 
 * <AUTHOR> Assistant System
 */
@Data
public class UpdateThemeRequest {
    
    /**
     * 界面主题：light-浅色模式，dark-深色模式
     */
    @NotBlank(message = "主题不能为空")
    @Pattern(regexp = "^(light|dark)$", message = "主题只能是light或dark")
    private String theme;
}
