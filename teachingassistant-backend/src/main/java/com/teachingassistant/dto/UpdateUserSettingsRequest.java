package com.teachingassistant.dto;

import lombok.Data;

import jakarta.validation.constraints.Pattern;

/**
 * 更新用户设置请求DTO
 * 
 * <AUTHOR> Assistant System
 */
@Data
public class UpdateUserSettingsRequest {
    
    /**
     * 消息提醒开关
     */
    private Boolean notificationEnabled;
    
    /**
     * 界面主题：light-浅色模式，dark-深色模式
     */
    @Pattern(regexp = "^(light|dark)$", message = "主题只能是light或dark")
    private String theme;
    
    /**
     * 语言设置
     */
    @Pattern(regexp = "^(zh-CN|en-US)$", message = "语言只能是zh-CN或en-US")
    private String language;
    
    /**
     * 课程提醒开关
     */
    private Boolean classReminder;
    
    /**
     * 约课提醒开关
     */
    private Boolean appointmentReminder;
    
    /**
     * 侧边栏折叠状态
     */
    private Boolean sidebarCollapsed;
}
