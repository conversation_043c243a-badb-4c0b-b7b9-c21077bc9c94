package com.teachingassistant.dto;

import jakarta.validation.constraints.*;
import lombok.Data;

/**
 * 更新教室请求DTO
 * 
 * <AUTHOR> Assistant System
 */
@Data
public class UpdateClassroomRequest {
    
    /**
     * 教室名称
     */
    @Size(max = 50, message = "教室名称长度不能超过50个字符")
    private String name;
    
    /**
     * 楼层
     */
    @Min(value = 1, message = "楼层必须大于0")
    @Max(value = 50, message = "楼层不能超过50")
    private Integer floor;
    
    /**
     * 教室类型
     */
    @Pattern(regexp = "^(normal|multimedia|lab)$", message = "教室类型必须是normal、multimedia或lab")
    private String type;
    
    /**
     * 容量（人数）
     */
    @Min(value = 1, message = "容量必须大于0")
    @Max(value = 500, message = "容量不能超过500")
    private Integer capacity;
    
    /**
     * 设备描述
     */
    @Size(max = 500, message = "设备描述长度不能超过500个字符")
    private String equipment;
    
    /**
     * 教室状态
     */
    @Pattern(regexp = "^(available|in_use|maintenance)$", message = "教室状态必须是available、in_use或maintenance")
    private String status;
}
