package com.teachingassistant.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 重置密码请求DTO
 * 
 * <AUTHOR> Assistant System
 */
@Data
public class ResetPasswordRequest {
    
    /**
     * 新密码
     */
    @NotBlank(message = "新密码不能为空")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,20}$", 
             message = "密码必须包含大小写字母和数字，长度6-20位")
    private String newPassword;
}
