package com.teachingassistant.dto;

import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;

/**
 * 创建用户请求DTO
 * 
 * <AUTHOR> Assistant System
 */
@Data
public class CreateUserRequest {
    
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_]{4,20}$", message = "用户名只能包含字母、数字、下划线，长度4-20位")
    private String username;
    
    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,20}$", 
             message = "密码必须包含大小写字母和数字，长度6-20位")
    private String password;
    
    /**
     * 真实姓名
     */
    @NotBlank(message = "真实姓名不能为空")
    private String realName;
    
    /**
     * 角色
     */
    @NotBlank(message = "角色不能为空")
    @Pattern(regexp = "^(super_admin|principal|teacher)$", message = "角色只能是super_admin、principal或teacher")
    private String role;
    
    /**
     * 学校ID
     */
    @NotNull(message = "学校ID不能为空")
    private Long schoolId;
    
    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 性别：M-男，F-女
     */
    @Pattern(regexp = "^(M|F)$", message = "性别只能是M或F")
    private String gender;
    
    /**
     * 入职时间
     */
    private String hireDate;
    
    /**
     * 主教科目（教师角色使用）
     */
    private String subject;
    
    /**
     * 教学经验年数（教师角色使用）
     */
    private Integer experience;
    
    /**
     * 个人简介
     */
    private String bio;
}
