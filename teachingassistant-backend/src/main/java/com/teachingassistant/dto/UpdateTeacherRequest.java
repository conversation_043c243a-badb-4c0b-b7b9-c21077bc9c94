package com.teachingassistant.dto;

import lombok.Data;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Pattern;
import java.util.List;

/**
 * 更新老师信息请求DTO
 * 
 * <AUTHOR> Assistant System
 */
@Data
public class UpdateTeacherRequest {
    
    /**
     * 真实姓名
     */
    private String realName;
    
    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    private String email;
    
    /**
     * 性别：M-男，F-女
     */
    @Pattern(regexp = "^(M|F)$", message = "性别只能是M或F")
    private String gender;
    
    /**
     * 主教科目（支持多选）
     */
    private List<String> subject;
    
    /**
     * 教学经验年数
     */
    private Integer experience;
    
    /**
     * 个人简介
     */
    private String bio;
    
    /**
     * 账号状态：active-激活，inactive-禁用
     */
    @Pattern(regexp = "^(active|inactive)$", message = "状态只能是active或inactive")
    private String status;
}
