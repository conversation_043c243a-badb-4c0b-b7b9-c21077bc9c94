package com.teachingassistant.util;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 科目数据处理工具类
 * 
 * <AUTHOR> Assistant System
 */
public class SubjectUtils {
    
    /**
     * 将科目数组转换为逗号分隔的字符串
     * 
     * @param subjects 科目数组
     * @return 逗号分隔的字符串
     */
    public static String arrayToString(List<String> subjects) {
        if (subjects == null || subjects.isEmpty()) {
            return null;
        }
        return subjects.stream()
                .filter(subject -> subject != null && !subject.trim().isEmpty())
                .map(String::trim)
                .collect(Collectors.joining(","));
    }
    
    /**
     * 将逗号分隔的字符串转换为科目数组
     * 
     * @param subjectString 逗号分隔的字符串
     * @return 科目数组
     */
    public static List<String> stringToArray(String subjectString) {
        if (subjectString == null || subjectString.trim().isEmpty()) {
            return List.of();
        }
        return Arrays.stream(subjectString.split(","))
                .map(String::trim)
                .filter(subject -> !subject.isEmpty())
                .collect(Collectors.toList());
    }
    
    /**
     * 验证科目是否有效
     * 
     * @param subject 科目名称
     * @return 是否有效
     */
    public static boolean isValidSubject(String subject) {
        if (subject == null || subject.trim().isEmpty()) {
            return false;
        }
        
        // 定义有效的科目列表
        List<String> validSubjects = Arrays.asList(
            "语文", "数学", "英语", "物理", "化学", "生物", 
            "历史", "地理", "政治", "音乐", "美术", "体育",
            "信息技术", "通用技术"
        );
        
        return validSubjects.contains(subject.trim());
    }
    
    /**
     * 验证科目数组是否都有效
     * 
     * @param subjects 科目数组
     * @return 是否都有效
     */
    public static boolean areValidSubjects(List<String> subjects) {
        if (subjects == null || subjects.isEmpty()) {
            return false;
        }
        return subjects.stream().allMatch(SubjectUtils::isValidSubject);
    }
}
