package com.teachingassistant.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.teachingassistant.common.PageResult;
import com.teachingassistant.dto.CreateSchoolRequest;
import com.teachingassistant.dto.UpdateSchoolRequest;
import com.teachingassistant.entity.School;
import com.teachingassistant.service.SchoolService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 学校控制器测试
 */
@WebMvcTest(SchoolController.class)
public class SchoolControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private SchoolService schoolService;

    @Autowired
    private ObjectMapper objectMapper;

    private School testSchool;

    @BeforeEach
    void setUp() {
        testSchool = new School();
        testSchool.setSchoolId(1L);
        testSchool.setName("测试学校");
        testSchool.setAddress("测试地址");
        testSchool.setPhone("010-12345678");
        testSchool.setEmail("<EMAIL>");
        testSchool.setAdminQuota(3);
        testSchool.setStatus("active");
        testSchool.setCreatedAt(LocalDateTime.now());
        testSchool.setUpdatedAt(LocalDateTime.now());
    }

    @Test
    void testGetSchoolList() throws Exception {
        List<School> schools = Arrays.asList(testSchool);
        PageResult<School> pageResult = PageResult.of(1, 10, 1L, schools);
        
        when(schoolService.findWithPagination(anyInt(), anyInt(), anyString(), anyString()))
                .thenReturn(pageResult);

        mockMvc.perform(get("/admin/schools")
                .param("page", "1")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.total").value(1))
                .andExpect(jsonPath("$.data.records[0].name").value("测试学校"));
    }

    @Test
    void testGetSchoolById() throws Exception {
        when(schoolService.findById(1L)).thenReturn(testSchool);

        mockMvc.perform(get("/admin/schools/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.name").value("测试学校"));
    }

    @Test
    void testCreateSchool() throws Exception {
        CreateSchoolRequest request = new CreateSchoolRequest();
        request.setName("新学校");
        request.setAddress("新地址");
        request.setPhone("010-87654321");
        request.setEmail("<EMAIL>");
        request.setAdminQuota(2);
        request.setStatus("active");

        when(schoolService.createSchool(any(School.class))).thenReturn(testSchool);

        mockMvc.perform(post("/admin/schools")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.name").value("测试学校"));
    }

    @Test
    void testUpdateSchool() throws Exception {
        UpdateSchoolRequest request = new UpdateSchoolRequest();
        request.setName("更新学校");
        request.setAddress("更新地址");

        when(schoolService.updateSchool(any(School.class))).thenReturn(testSchool);

        mockMvc.perform(put("/admin/schools/1")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }

    @Test
    void testDeleteSchool() throws Exception {
        mockMvc.perform(delete("/admin/schools/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));
    }
}
