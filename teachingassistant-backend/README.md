# 助教排课系统后端

## 项目简介

助教排课系统是一个基于Spring Boot 3.4.7的现代化教育管理系统，支持多角色权限管理、智能排课、教室管理等功能。

## 技术栈

- **框架**: Spring Boot 3.4.7
- **JDK**: 17
- **数据库**: MySQL 8.0
- **缓存**: Redis 5.0
- **ORM**: MyBatis 3.0.4
- **安全**: Spring Security + JWT
- **数据库迁移**: Flyway
- **构建工具**: Maven 3.9.10

## 系统架构

### 角色权限体系
- **超级管理员**: 管理多学校、校长分配
- **校长**: 管理单所学校的人员、教学、运营
- **老师**: 管理个人教学事务、学生管理

### 核心功能模块
1. **认证授权模块**: JWT Token认证、角色权限控制
2. **用户管理模块**: 多角色用户管理、个人中心
3. **学校管理模块**: 学校信息管理、校长分配
4. **教学管理模块**: 智能排课、教室管理、班级管理
5. **运营管理模块**: 约课中心、消息管理
6. **财务管理模块**: 工资管理、学费管理

## 快速开始

### 环境要求

- JDK 17+
- MySQL 8.0+
- Redis 5.0+
- Maven 3.6+

### 数据库配置

1. 创建数据库：
```sql
CREATE DATABASE teaching_assistant CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改配置文件 `src/main/resources/application.yaml` 中的数据库连接信息：
```yaml
spring:
  datasource:
    url: ************************************************************************************************************************************************************
    username: root
    password: 123456
```

### 启动应用

1. 编译项目：
```bash
mvn clean compile
```

2. 运行数据库迁移：
```bash
mvn flyway:migrate
```

3. 启动应用：
```bash
mvn spring-boot:run
```

应用启动后访问地址：http://localhost:8080/api

### 测试接口

#### 1. 健康检查（无需认证）
```bash
curl -X GET http://localhost:8080/api/test/health
```

#### 2. 用户登录
```bash
curl -X POST http://localhost:8080/api/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

#### 3. 获取当前用户信息（需要认证）
```bash
curl -X GET http://localhost:8080/api/user/profile \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 默认账户

系统初始化时会创建以下默认账户：

### 超级管理员
- 用户名: `admin`
- 密码: `admin123`
- 角色: 超级管理员

### 校长账户
- 用户名: `principal001` (北京市第一中学)
- 用户名: `principal002` (上海市实验中学)  
- 用户名: `principal003` (广州市育才学校)
- 密码: `admin123`
- 角色: 校长

### 老师账户
- 用户名: `teacher001` ~ `teacher006`
- 密码: `admin123`
- 角色: 老师

## API文档

### 认证相关接口

| 方法 | 路径 | 描述 | 权限 |
|------|------|------|------|
| POST | /login | 用户登录 | 公开 |
| POST | /logout | 用户登出 | 公开 |
| GET | /user/profile | 获取当前用户信息 | 需认证 |
| POST | /refresh-token | 刷新Token | 需认证 |

### 测试接口

| 方法 | 路径 | 描述 | 权限 |
|------|------|------|------|
| GET | /test/health | 健康检查 | 公开 |
| GET | /test/public | 公开接口测试 | 公开 |
| GET | /test/protected | 受保护接口测试 | 需认证 |

## 数据库设计

### 核心表结构

1. **schools**: 学校信息表
2. **users**: 用户信息表（多角色）
3. **students**: 学生信息表
4. **classes**: 班级信息表
5. **classrooms**: 教室信息表
6. **courses**: 课程排期表（排课核心）
7. **attendances**: 考勤记录表
8. **salaries**: 工资明细表
9. **payments**: 财务缴费表
10. **messages**: 消息通知表
11. **operation_logs**: 操作日志表
12. **bookings**: 约课信息表
13. **user_settings**: 用户设置表
14. **teacher_student**: 师生关联表

## 开发指南

### 项目结构
```
src/main/java/com/teachingassistant/
├── TeachingAssistantApplication.java  # 主应用类
├── common/                            # 通用类
│   ├── Result.java                   # 统一响应格式
│   ├── ResultCode.java               # 响应状态码
│   └── PageResult.java               # 分页结果
├── config/                           # 配置类
│   ├── SecurityConfig.java           # 安全配置
│   └── JwtProperties.java            # JWT配置
├── controller/                       # 控制器
├── dto/                             # 数据传输对象
├── entity/                          # 实体类
├── exception/                       # 异常处理
├── mapper/                          # 数据访问层
├── security/                        # 安全相关
├── service/                         # 业务服务层
└── util/                           # 工具类
```

### 代码规范

1. 所有类都要添加作者注释
2. 使用Lombok简化代码
3. 统一使用Result包装响应结果
4. 异常统一使用BusinessException
5. 数据库操作使用事务注解

## 部署说明

### Docker部署（推荐）

1. 构建镜像：
```bash
mvn clean package
docker build -t teaching-assistant-backend .
```

2. 运行容器：
```bash
docker run -d \
  --name teaching-assistant \
  -p 8080:8080 \
  -e SPRING_DATASOURCE_URL=***************************************** \
  -e SPRING_DATASOURCE_USERNAME=root \
  -e SPRING_DATASOURCE_PASSWORD=password \
  teaching-assistant-backend
```

### 传统部署

1. 打包应用：
```bash
mvn clean package -DskipTests
```

2. 运行JAR包：
```bash
java -jar target/teachingassistant-backend-1.0.0.jar
```

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库连接配置是否正确
- 检查防火墙设置

### 2. Redis连接失败
- 检查Redis服务是否启动
- 确认Redis连接配置是否正确

### 3. JWT Token无效
- 检查Token是否过期
- 确认请求头格式：`Authorization: Bearer <token>`

## 联系方式

如有问题，请联系开发团队。
