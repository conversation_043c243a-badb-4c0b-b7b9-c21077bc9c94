助教排课系统需求文档
该系统仅使用一个界面端，里面包含所有功能，通过角色区分来显示不同的功能。比如左侧是总的菜单列表。管理员能够看到所有的菜单，而校长角色无法看到多学校管理功能，老师无法看到排课功能。

技术架构：
前端：node,js22.14.0,Vue3/Vite 模板,Typescript
后端：java springboot3.4.7 ,JDK 17.0.14,maven3.9.10
数据库：mysql8.0
缓存：Redis5.0.14.1
数据库管理：flyway

一．系统基础模块
1.登录功能
1.1账号密码登录
1.2验证码验证
1.3角色区分(校长/老师)登录
2.个人中心
2.1账号信息管理(修改密码、绑定手机等)
2.2系统设置(消息提醒设置、界面风格等)
2.3操作日志查看(查看个人登录及功能使用记录)

二．校长角色核心功能模块
1.人员信息管理
1.1老师查询：可查询所有老师信息，点击可查看其关联学生及工资详情
1.2学生信息：可查看所有学生信息，点击可显示基本信息及未付款金额
2.教学管理
2.1排课流程：查看老师信息后选定老师，设置年级、姓名、教师、价格和时间完成排课，系统自动检测并提示排课冲突
(排课具体流程：排课流程 -选择开始日期-结束日期，选星期一到星期天，老师，年级，学生姓名，教室，价格和时间，自动生成某一个学生的一段时间排课数据。后期要改排课信息，就去选中某一条数据去修改时间和价格)
2.2教室查询：实时查看各教室上课情况，包含授课老师及上课学生信息
2.3班级管理：全量管理班级信息，支持创建、修改、删除操作，系统展示各班学生及老师详情
3.运营管理
3.1约课相关：接收学生扫码发送的约课信息(系统自动处理)，支持查看和管理约课消息
3.2消息管理：统一接收并管理约课信息、学校通知等各类消息
4.财务管理关联
4.1间接通过老师查询功能关联查看老师工资信息
4.2间接通过学生信息功能关联查看学生未付款金额

三．老师角色核心功能模块
1.教学工作管理
1.1课程表查看：查看个人课程表，系统清晰展示每节课的时间、地点及课程内容
1.2考勤记录：每节课后记录学生出勤情况，系统自动保存，作为课时工资计算依据
2.学生管理
2.1学生列表及详情：查看个人负责学生列表(显示姓名、班级、联系方式)，点击可查看学生基本信息(含出勤情况、学习进度)
3.个人事务管理
3.1工资查询：查询个人工资信息，系统详细展示每月工资构成及发放时间
3.2消息管理：接收并管理学校通知、学生家长沟通信息等各类信息

四.管理员角色核心功能
1. 学校管理：
   		1.1创建/编辑学校（学校名称、地址、管理员配额）
   		1.2设置学校状态（启用/停用）
   
2. 校长管理：
   		2.1为学校分配校长（支持批量导入）
   		2.2重置校长密码/解绑MFA设备

3.用户管理：
3.1对各种角色，各种用户进行增删改查
