### 助教排课系统需求文档

### 系统技术选型
**前端**：Node.js 22.14.0 + Vue3/Vite + TypeScript  
**后端**：Spring Boot 3.4.7 + JDK 17 + Maven 3.9.10  
**数据库**：MySQL 8.0  
**缓存**：Redis ********  

---

### 一、系统基础模块

#### 1. 多角色登录与权限控制
1.1 角色体系：
   - 超级管理员（管理多学校/校长）
   - 校长（管理单所学校）
   - 老师（管理个人教学事务）

1.2 登录方式：
   - 账号密码登录（系统自动识别角色）
   - 登录后动态加载权限菜单（RBAC模型）

1.3 权限控制机制：
   - 前端：通过Vue Router守卫动态渲染菜单
   - 后端：Spring Security + JWT实现接口级权限校验
   - 数据隔离：校长/老师仅能访问所属学校数据

#### 2. 个人中心（全角色通用）
2.1 账号管理：密码修改、手机绑定  
2.2 系统设置：消息提醒策略、UI主题切换  
2.3 操作日志：按时间轴展示用户操作记录  

---

### 二、超级管理员端核心功能

#### 1. 多学校管理
1.1 学校管理：
   - 创建/编辑学校（学校名称、地址、管理员配额）
   - 设置学校状态（启用/停用）
   
1.2 校长管理：
   - 为学校分配校长（支持批量导入）
   - 重置校长密码/解绑MFA设备

#### 2. 全局监控
2.1 跨校数据看板：
   - 各校老师/学生数量统计
   - 学校课程饱和度热力图  
2.2 操作审计：
   - 查看所有校长的关键操作日志
   - 异常行为告警（如单日删除超50条数据）

---

### 三、校长端核心功能（数据隔离至所属学校）

#### 1. 人员管理
1.1 老师管理：
   - 增删改查本校老师信息
   - 老师-学生关联关系可视化（树状图）
   
1.2 学生管理：
   - 学生信息导入（Excel模板）
   - 未付款金额批量导出（CSV）

#### 2. 教学管理
2.1 智能排课系统：
   - 冲突检测算法：实时校验「老师/教室/时间」三维冲突
   - 批量排课：按周循环生成课程（支持排除节假日）
   - 课程调整：拖拽式课表修改（自动通知相关老师）

2.2 教室资源管理：
   - 教室状态看板（使用中/空闲/维护中）
   - 教室使用率分析报表

#### 3. 运营管理
3.1 约课中心：
   - 学生扫码约课自动匹配空闲师资
   - 约课冲突智能推荐解决方案
   
3.2 消息中枢：
   - 分类聚合约课/缴费/系统通知
   - 紧急消息强提醒（弹窗+短信）

#### 4. 财务关联
4.1 教师薪酬：
   - 按课时自动生成工资明细
   - 支持自定义绩效规则（出勤率挂钩）
   
4.2 学生账务：
   - 未缴费学生自动提醒任务
   - 历史缴费记录追溯（按学年）

---

### 四、老师端核心功能

#### 1. 教学管理
1.1 智能课表：
   - 日/周/月视图切换
   - 课前1小时自动推送提醒

1.2 考勤系统：
   - 人脸识别快速签到
   - 缺勤学生家长自动通知

#### 2. 学生管理
2.1 学生档案：
   - 学习进度雷达图（知识点掌握度）
   - 家长联系方式一键拨打（Web端集成CTI）

#### 3. 个人事务
3.1 薪酬系统：
   - 工资明细可钻取（课时费/绩效奖金/扣款项）
   - 个税计算器
   
3.2 消息中心：
   - 按优先级过滤消息
   - 校长通知强提醒（需阅读确认）

---

### 五、权限控制方案

#### 1. RBAC模型设计
```mermaid
graph TD
    A[超级管理员] -->|管理| B(学校)
    B --> C[校长1]
    B --> D[校长2]
    C -->|管理| E[老师A]
    C -->|管理| F[老师B]
    D -->|管理| G[老师C]