# Java相关
*.class
*.log
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Maven相关
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Spring Boot相关
!**/src/main/**/target/
!**/src/test/**/target/

# Node.js相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Vue.js/前端构建相关
dist/
dist-ssr/
*.local
.vite/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# IntelliJ IDEA
*.iml
*.ipr
*.iws
.idea/
out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

# NetBeans
nbproject/private/
build/
nbbuild/
dist/
nbdist/
.nb-gradle/

# Visual Studio Code
.vscode/

# 操作系统相关
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# 数据库相关
*.db
*.sqlite
*.sqlite3

# 日志文件
logs/
*.log

# 临时文件
tmp/
temp/
*.tmp
*.temp
*.cache

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 包管理器相关
package-lock.json
yarn.lock
pnpm-lock.yaml

# 备份文件
*.bak
*.backup
*.orig

# JetBrains IDE
.idea/
*.iml
*.ipr
*.iws

# 编译输出
build/
out/

# 测试相关
coverage/
*.lcov
.nyc_output/

# 其他
*.pid
*.seed
*.pid.lock
.grunt
.lock-wscript
.coverage
.cache
.parcel-cache
.next
.nuxt
.vuepress/dist
.serverless
.fusebox/
.dynamodb/

# 本项目特有的忽略项
# 可根据实际需要添加或移除以下内容
teachingassistant-backend/target/
teachingassistant-front/node_modules/
teachingassistant-front/dist/