-- 助教排课系统数据库初始化脚本
-- 请在MySQL中执行此脚本来创建数据库

-- 创建数据库
CREATE DATABASE IF NOT EXISTS teaching_assistant 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE teaching_assistant;

-- 显示创建结果
SELECT 'teaching_assistant数据库创建成功！' as message;
SELECT 'Character Set: utf8mb4' as charset;
SELECT 'Collation: utf8mb4_unicode_ci' as collation;

-- 显示使用说明
SELECT '请按以下步骤继续：' as next_steps;
SELECT '1. 确保MySQL服务正在运行' as step1;
SELECT '2. 修改后端配置文件中的数据库连接信息' as step2;
SELECT '3. 运行 start-backend.bat 启动后端服务' as step3;
SELECT '4. 运行 start-frontend.bat 启动前端服务' as step4;
