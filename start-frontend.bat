@echo off
echo ========================================
echo 助教排课系统前端启动脚本
echo ========================================

cd teachingassistant-front

echo 正在检查Node.js环境...
node -v
if %errorlevel% neq 0 (
    echo 错误：未找到Node.js环境，请确保已安装Node.js 20.19.0+
    pause
    exit /b 1
)

echo.
echo 正在检查npm环境...
npm -v
if %errorlevel% neq 0 (
    echo 错误：未找到npm环境
    pause
    exit /b 1
)

echo.
echo 正在安装依赖...
npm install
if %errorlevel% neq 0 (
    echo 错误：依赖安装失败
    pause
    exit /b 1
)

echo.
echo 正在启动开发服务器...
echo 访问地址：http://localhost:5173
echo.
npm run dev

pause
