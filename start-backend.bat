@echo off
echo ========================================
echo 助教排课系统后端启动脚本
echo ========================================

cd teachingassistant-backend

echo 正在检查Java环境...
java -version
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请确保已安装JDK 17+
    pause
    exit /b 1
)

echo.
echo 正在检查Maven环境...
mvn -version
if %errorlevel% neq 0 (
    echo 错误：未找到Maven环境，请确保已安装Maven 3.6+
    pause
    exit /b 1
)

echo.
echo 正在编译项目...
mvn clean compile
if %errorlevel% neq 0 (
    echo 错误：项目编译失败
    pause
    exit /b 1
)

echo.
echo 正在执行数据库迁移...
mvn flyway:migrate
if %errorlevel% neq 0 (
    echo 警告：数据库迁移失败，请检查数据库连接配置
    echo 请确保：
    echo 1. MySQL服务已启动
    echo 2. 已创建teaching_assistant数据库
    echo 3. 数据库连接配置正确
    pause
)

echo.
echo 正在启动应用...
echo 访问地址：http://localhost:8080/api
echo 健康检查：http://localhost:8080/api/test/health
echo.
mvn spring-boot:run

pause
