# 教室管理API测试指南

## 系统状态
- ✅ 后端服务：http://localhost:8081/api
- ✅ 前端应用：http://localhost:3001
- ✅ 数据库已迁移，包含新的教室字段

## 测试步骤

### 1. 登录系统
访问 http://localhost:3001，使用校长账号登录

### 2. 访问教室管理
导航到：校长端 → 教室管理

### 3. 测试功能

#### 3.1 查看教室列表
- 应该显示当前学校的教室列表
- 支持分页显示
- 显示教室名称、楼层、类型、容量、设备、状态等信息

#### 3.2 搜索和筛选
- 按教室名称搜索
- 按楼层筛选
- 按教室类型筛选（普通教室/多媒体教室/实验室）
- 按状态筛选（可用/使用中/维护中）

#### 3.3 创建教室
点击"添加教室"按钮，填写：
- 教室名称（必填）
- 楼层（1-50）
- 类型（普通教室/多媒体教室/实验室）
- 容量（1-500人）
- 设备描述
- 状态（可用/使用中/维护中）

#### 3.4 编辑教室
- 点击教室列表中的"编辑"按钮
- 修改教室信息
- 保存更改

#### 3.5 删除教室
- 点击教室列表中的"删除"按钮
- 确认删除操作

## API接口测试

### 校长端接口
```
GET /api/principal/classrooms?page=1&size=10
POST /api/principal/classrooms
PUT /api/principal/classrooms/{id}
DELETE /api/principal/classrooms/{id}
```

### 管理员端接口
```
GET /api/admin/classrooms?page=1&size=10
POST /api/admin/classrooms
PUT /api/admin/classrooms/{id}
DELETE /api/admin/classrooms/{id}
```

## 权限验证
- 校长只能管理本校教室
- 管理员可以管理所有学校的教室
- 未登录用户无法访问

## 数据验证
- 教室名称：必填，最大50字符
- 楼层：1-50之间的整数
- 容量：1-500之间的整数
- 设备描述：最大500字符
- 同一学校内教室名称不能重复

## 预期结果
所有操作应该正常工作，数据能够正确保存和显示，权限控制生效。
