# 助教排课系统

一个基于Spring Boot + Vue 3的现代化教育管理系统，支持多角色权限管理、智能排课、教室管理等功能。

## 系统概述

助教排课系统是为教育机构设计的综合管理平台，通过统一的界面为不同角色的用户提供相应的功能模块。系统采用前后端分离架构，具有良好的扩展性和维护性。

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.4.7
- **JDK**: 17
- **数据库**: MySQL 8.0
- **缓存**: Redis 5.0
- **ORM**: MyBatis 3.0.4
- **安全**: Spring Security + JWT
- **数据库迁移**: Flyway
- **构建工具**: Maven 3.9.10

### 前端技术栈
- **框架**: Vue 3.5.18
- **构建工具**: Vite 7.0.6
- **语言**: TypeScript 5.7.2
- **UI框架**: Element Plus 2.9.1
- **状态管理**: Pinia 2.3.0
- **路由**: Vue Router 4.5.0
- **HTTP客户端**: Axios 1.7.9

## 功能模块

### 角色权限体系

#### 超级管理员 (super_admin)
- 学校管理：创建/编辑学校、设置学校状态
- 校长管理：为学校分配校长、重置密码
- 用户管理：对各种角色用户进行增删改查

#### 校长 (principal)
- **人员信息管理**
  - 老师查询：查看所有老师信息及关联学生、工资详情
  - 学生信息：查看所有学生信息及未付款金额
- **教学管理**
  - 排课流程：智能排课，自动检测冲突
  - 教室查询：实时查看各教室上课情况
  - 班级管理：全量管理班级信息
- **运营管理**
  - 约课相关：处理学生约课信息
  - 消息管理：统一管理各类消息
- **财务管理**
  - 间接查看老师工资信息和学生未付款金额

#### 老师 (teacher)
- **教学工作管理**
  - 课程表查看：查看个人课程安排
  - 考勤记录：记录学生出勤情况
- **学生管理**
  - 学生列表及详情：查看负责学生的基本信息
- **个人事务管理**
  - 工资查询：查询个人工资信息
  - 消息管理：接收学校通知等信息

### 核心功能特性

1. **智能排课系统**
   - 自动检测时间冲突
   - 支持批量排课
   - 灵活的时间调整

2. **权限控制**
   - 基于角色的访问控制
   - 细粒度权限管理
   - 安全的JWT认证

3. **响应式设计**
   - 支持桌面端和移动端
   - 自适应布局
   - 优秀的用户体验

4. **数据管理**
   - 完整的数据备份
   - 版本化数据库迁移
   - 数据完整性保证

## 快速开始

### 环境要求

- JDK 17+
- Node.js 20.19.0+ 或 22.12.0+
- MySQL 8.0+
- Redis 5.0+
- Maven 3.6+

### 后端启动

1. 克隆项目
```bash
git clone <repository-url>
cd teachingassistant
```

2. 配置数据库
```sql
CREATE DATABASE teaching_assistant CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

3. 修改配置文件
```bash
cd teachingassistant-backend
# 编辑 src/main/resources/application.yaml
# 修改数据库连接信息
```

4. 启动后端服务
```bash
mvn clean compile
mvn flyway:migrate
mvn spring-boot:run
```

后端服务地址：http://localhost:8080/api

### 前端启动

1. 安装依赖
```bash
cd teachingassistant-front
npm install
```

2. 启动开发服务器
```bash
npm run dev
```

前端访问地址：http://localhost:5173

## 默认账户

系统初始化后提供以下测试账户：

### 超级管理员
- 用户名: `admin`
- 密码: `admin123`

### 校长账户
- 用户名: `principal001` (北京市第一中学)
- 用户名: `principal002` (上海市实验中学)
- 用户名: `principal003` (广州市育才学校)
- 密码: `admin123`

### 老师账户
- 用户名: `teacher001` ~ `teacher006`
- 密码: `admin123`

## 项目结构

```
teachingassistant/
├── teachingassistant-backend/     # 后端项目
│   ├── src/main/java/            # Java源码
│   ├── src/main/resources/       # 配置文件和资源
│   ├── src/test/                 # 测试代码
│   └── pom.xml                   # Maven配置
├── teachingassistant-front/      # 前端项目
│   ├── src/                      # 源码目录
│   ├── public/                   # 静态资源
│   ├── package.json              # 依赖配置
│   └── vite.config.js           # Vite配置
├── 助教系统接口文档.json          # API接口文档
├── 助教系统建表语句.sql           # 数据库建表脚本
├── 助教系统补充需求.md            # 补充需求说明
└── README.md                     # 项目说明
```

## API文档

详细的API接口文档请参考 `助教系统接口文档.json` 文件。

主要接口包括：
- 用户认证接口
- 学校管理接口
- 用户管理接口
- 学生管理接口
- 课程管理接口
- 排课管理接口
- 消息管理接口

## 数据库设计

数据库建表语句请参考 `助教系统建表语句.sql` 文件。

核心表结构：
- `schools` - 学校信息表
- `users` - 用户信息表
- `students` - 学生信息表
- `classes` - 班级信息表
- `classrooms` - 教室信息表
- `courses` - 课程排期表
- `attendances` - 考勤记录表
- `salaries` - 工资明细表
- `messages` - 消息通知表

## 部署说明

### Docker部署（推荐）

1. 构建后端镜像
```bash
cd teachingassistant-backend
mvn clean package
docker build -t teaching-assistant-backend .
```

2. 构建前端镜像
```bash
cd teachingassistant-front
npm run build
docker build -t teaching-assistant-frontend .
```

3. 使用Docker Compose启动
```bash
docker-compose up -d
```

### 传统部署

1. 后端部署
```bash
cd teachingassistant-backend
mvn clean package -DskipTests
java -jar target/teachingassistant-backend-1.0.0.jar
```

2. 前端部署
```bash
cd teachingassistant-front
npm run build
# 将dist目录部署到Web服务器
```

## 开发指南

### 后端开发
- 遵循Spring Boot最佳实践
- 使用MyBatis进行数据访问
- 统一异常处理和响应格式
- 完善的日志记录

### 前端开发
- 使用Vue 3 Composition API
- TypeScript类型安全
- Element Plus组件库
- 响应式设计原则

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请联系开发团队。

---

**助教排课系统** - 让教育管理更简单、更高效！
