openapi: 3.0.3
info:
  title: 老师管理接口更新
  description: 助教系统老师管理功能的接口更新，支持多选科目和完整信息回显
  version: 1.0.0
  contact:
    name: Teaching Assistant System
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: 本地开发环境
  - url: https://api.teachingassistant.com
    description: 生产环境

paths:
  /admin/teachers/{teacherId}:
    get:
      tags:
        - 管理员-老师管理
      summary: 获取老师详细信息
      description: 根据老师ID获取老师的详细信息，用于编辑时的信息回显
      parameters:
        - name: teacherId
          in: path
          required: true
          description: 老师用户ID
          schema:
            type: integer
            format: int64
            example: 50001
      responses:
        '200':
          description: 获取成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "操作成功"
                  data:
                    $ref: '#/components/schemas/TeacherDetailInfo'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 老师不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

    put:
      tags:
        - 管理员-老师管理
      summary: 更新老师信息
      description: 更新老师的基本信息，支持多选科目
      parameters:
        - name: teacherId
          in: path
          required: true
          description: 老师用户ID
          schema:
            type: integer
            format: int64
            example: 50001
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTeacherRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  message:
                    type: string
                    example: "操作成功"
                  data:
                    $ref: '#/components/schemas/TeacherDetailInfo'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 老师不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    TeacherDetailInfo:
      type: object
      description: 老师详细信息
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 50001
        username:
          type: string
          description: 登录账号
          example: "teacher001"
        realName:
          type: string
          description: 真实姓名
          example: "张老师"
        role:
          type: string
          description: 用户角色
          example: "teacher"
        schoolId:
          type: integer
          format: int64
          description: 所属学校ID
          example: 1
        schoolName:
          type: string
          description: 学校名称
          example: "示例中学"
        principalId:
          type: integer
          format: int64
          description: 所属校长ID
          example: 30001
        phone:
          type: string
          description: 手机号
          example: "13800138000"
        email:
          type: string
          description: 邮箱地址
          example: "<EMAIL>"
        gender:
          type: string
          description: 性别
          enum: ["M", "F"]
          example: "F"
        hireDate:
          type: string
          description: 入职时间
          example: "2023-09-01"
        subject:
          type: string
          description: 主教科目（逗号分隔）
          example: "数学,物理"
        subjectArray:
          type: array
          description: 主教科目数组（用于前端显示）
          items:
            type: string
          example: ["数学", "物理"]
        experience:
          type: integer
          description: 教学经验年数
          example: 5
        bio:
          type: string
          description: 个人简介
          example: "专业数学老师，擅长高中数学教学"
        status:
          type: string
          description: 账号状态
          enum: ["active", "inactive"]
          example: "active"
        lastLoginAt:
          type: string
          description: 最后登录时间
          example: "2024-01-15 10:30:00"
        createdAt:
          type: string
          description: 创建时间
          example: "2023-09-01 09:00:00"
        updatedAt:
          type: string
          description: 更新时间
          example: "2024-01-15 10:30:00"
        principal:
          type: object
          description: 所属校长信息
          properties:
            userId:
              type: integer
              format: int64
              example: 30001
            realName:
              type: string
              example: "李校长"
            phone:
              type: string
              example: "13900139000"
            email:
              type: string
              example: "<EMAIL>"

    UpdateTeacherRequest:
      type: object
      description: 更新老师信息请求
      properties:
        realName:
          type: string
          description: 真实姓名
          example: "张老师"
        phone:
          type: string
          description: 手机号
          pattern: '^1[3-9]\d{9}$'
          example: "13800138000"
        email:
          type: string
          description: 邮箱地址
          format: email
          example: "<EMAIL>"
        gender:
          type: string
          description: 性别
          enum: ["M", "F"]
          example: "F"
        subject:
          type: array
          description: 主教科目（支持多选）
          items:
            type: string
            enum: ["语文", "数学", "英语", "物理", "化学", "生物", "历史", "地理", "政治", "音乐", "美术", "体育", "信息技术", "通用技术"]
          example: ["数学", "物理"]
        experience:
          type: integer
          description: 教学经验年数
          minimum: 0
          maximum: 50
          example: 5
        bio:
          type: string
          description: 个人简介
          example: "专业数学老师，擅长高中数学教学"
        status:
          type: string
          description: 账号状态
          enum: ["active", "inactive"]
          example: "active"

    ErrorResponse:
      type: object
      description: 错误响应
      properties:
        code:
          type: integer
          description: 错误代码
          example: 400
        message:
          type: string
          description: 错误信息
          example: "请求参数错误"
        data:
          type: object
          description: 错误详情
          nullable: true

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

security:
  - BearerAuth: []

tags:
  - name: 管理员-老师管理
    description: 管理员端老师管理相关接口
